
export type HabitType = 'check-off' | 'measurable' | 'timed';

export interface Habit {
  id: string;
  name: string;
  type: HabitType;
  streak: number;
  longestStreak?: number;
  isCompletedToday: boolean;
  history: { [date: string]: boolean }; // e.g. { '2023-10-27': true }
  lastCompletedTimestamp?: Date;
  timeWindow?: { start: string; end: string };
  targetValue?: number;
  targetUnit?: string;
}

export interface User {
  id: string;
  name: string;
  avatar: string;
  habits: Habit[];
}

export interface ActivityLog {
  id:string;
  userId: string;
  userName: string;
  userAvatar: string;
  action: string;
  timestamp: Date;
  suspect?: boolean;
}