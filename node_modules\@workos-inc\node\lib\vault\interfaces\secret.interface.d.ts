import { KeyContext } from './key.interface';
import { ObjectDigest, ObjectUpdateBy, ObjectMetadata, VaultObject, ObjectVersion } from './object.interface';
export interface SecretContext extends KeyContext {
}
export interface SecretDigest extends ObjectDigest {
}
export interface SecretUpdateBy extends ObjectUpdateBy {
}
export interface SecretMetadata extends ObjectMetadata {
}
export interface VaultSecret extends VaultObject {
}
export interface SecretVersion extends ObjectVersion {
}
