<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>TeamStreak</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=DM+Sans:opsz,wght@9..40,400;9..40,500;9..40,700&family=Unbounded:wght@700;900&family=Bebas+Neue&family=Inter:wght@400;500;600;700&family=Pirata+One&display=swap" rel="stylesheet">
    <style>
      html {
        scroll-behavior: smooth;
      }
      body {
        font-family: 'DM Sans', sans-serif;
        background-color: #101010;
      }
      .font-unbounded {
        font-family: 'Unbounded', sans-serif;
      }
      .font-brand {
        font-family: 'Pirata One', cursive;
        letter-spacing: 1px;
      }
      .font-stats {
        font-family: 'Bebas Neue', sans-serif;
      }
      /* Custom Scrollbar for Webkit Browsers */
      ::-webkit-scrollbar {
        width: 8px;
        height: 8px;
      }
      ::-webkit-scrollbar-track {
        background: #1a1a1a;
      }
      ::-webkit-scrollbar-thumb {
        background: #444;
        border-radius: 4px;
      }
      ::-webkit-scrollbar-thumb:hover {
        background: #555;
      }
      @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
      }
      .animate-fade-in {
        animation: fadeIn 0.3s ease-out forwards;
      }
      @keyframes fadeInUp {
          from { opacity: 0; transform: translateY(20px); }
          to { opacity: 1; transform: translateY(0); }
      }
      .animate-fade-in-up {
          animation: fadeInUp 0.5s ease-out forwards;
      }
      .prose-invert { color: #d1d5db; }
      .prose-invert h1, .prose-invert h2, .prose-invert h3, .prose-invert h4, .prose-invert p, .prose-invert ul, .prose-invert li, .prose-invert a { color: #d1d5db; }
      .prose-invert a { color: #10B981; text-decoration: underline; }
      .prose-invert ul { list-style-type: disc; padding-left: 1.5rem; }
      .prose-invert li { margin-top: 0.5rem; margin-bottom: 0.5rem; }
      .prose-lg p, .prose-lg ul, .prose-lg li { font-size: 1.125rem; line-height: 1.75rem; }
    </style>
  <script type="importmap">
{
  "imports": {
    "react/": "https://esm.sh/react@^19.1.0/",
    "react": "https://esm.sh/react@^19.1.0",
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/"
  }
}
</script>
<link rel="stylesheet" href="/index.css">
</head>
  <body>
    <div id="root"></div>
    <script type="module" src="/index.tsx"></script>
  </body>
</html>