
import React from 'react';

type ButtonVariant = 'yellow' | 'black' | 'white-outline';

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant: ButtonVariant;
  children: React.ReactNode;
  className?: string;
}

const Button: React.FC<ButtonProps> = ({ variant, children, className = '', ...props }) => {
  const baseClasses = 'px-6 py-3 rounded-full font-semibold transition-transform duration-200 ease-in-out hover:scale-105 active:scale-95';

  const variantClasses = {
    yellow: 'bg-[#047857] text-white',
    black: 'bg-black text-white',
    'white-outline': 'bg-transparent border border-gray-300 text-black hover:bg-black/5',
  };

  return (
    <button className={`${baseClasses} ${variantClasses[variant]} ${className}`} {...props}>
      {children}
    </button>
  );
};

export default Button;