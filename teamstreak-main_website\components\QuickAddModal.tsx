
import React, { useState, useEffect, useCallback } from 'react';
import { PlusIcon, CheckIcon, TargetIcon, StopwatchIcon, ClockIcon } from './icons';
import type { Habit, HabitType } from '../types';

interface QuickAddModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAddHabit: (habit: Partial<Habit>) => void;
}

interface HabitTypeButtonProps {
    type: HabitType;
    label: string;
    selected: boolean;
    onClick: (type: HabitType) => void;
    children: React.ReactNode;
}

const HabitTypeButton: React.FC<HabitTypeButtonProps> = ({ type, label, selected, onClick, children }) => (
    <button
        type="button"
        onClick={() => onClick(type)}
        className={`flex-1 p-3 rounded-lg border-2 flex flex-col items-center justify-center gap-2 transition-all duration-200
            ${selected
                ? 'bg-emerald-500/10 border-emerald-500 text-emerald-400'
                : 'bg-gray-800/50 border-gray-700 text-gray-400 hover:border-gray-500 hover:text-white'
            }`
        }
    >
        {children}
        <span className="text-xs font-semibold">{label}</span>
    </button>
)

const QuickAddModal: React.FC<QuickAddModalProps> = ({ isOpen, onClose, onAddHabit }) => {
  const [name, setName] = useState('');
  const [type, setType] = useState<HabitType>('check-off');
  const [showTimeWindow, setShowTimeWindow] = useState(false);
  const [startTime, setStartTime] = useState('09:00');
  const [endTime, setEndTime] = useState('17:00');

  const [isRendering, setIsRendering] = useState(false);

  useEffect(() => {
    if (isOpen) {
      setIsRendering(true);
    } else {
      // Let animation finish before unmounting
      const timer = setTimeout(() => setIsRendering(false), 300);
      return () => clearTimeout(timer);
    }
  }, [isOpen]);
  
  const handleClose = useCallback(() => {
    onClose();
  }, [onClose]);

  useEffect(() => {
    if (!isOpen) {
      // Reset form state when modal closes
      setName('');
      setType('check-off');
      setShowTimeWindow(false);
    }
  }, [isOpen]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (name.trim()) {
      const newHabit: Partial<Habit> = {
        name,
        type,
        timeWindow: showTimeWindow ? { start: startTime, end: endTime } : undefined,
      };
      onAddHabit(newHabit);
    }
  };

  if (!isRendering) return null;

  const backdropClasses = isOpen 
    ? 'opacity-100'
    : 'opacity-0';
  
  const modalClasses = isOpen
    ? 'opacity-100 scale-100'
    : 'opacity-0 scale-95';


  return (
    <div 
        className={`fixed inset-0 bg-black/70 z-50 flex items-center justify-center backdrop-blur-sm transition-opacity duration-300 ${backdropClasses}`}
        onClick={handleClose}
        role="dialog"
        aria-modal="true"
        aria-labelledby="modal-title"
    >
      <div 
        className={`bg-gradient-to-br from-gray-900 to-black border border-emerald-500/30 rounded-2xl p-8 max-w-lg w-full relative shadow-2xl shadow-emerald-500/20 transform transition-all duration-300 ${modalClasses}`}
        onClick={e => e.stopPropagation()}
    >
        <button 
            onClick={handleClose} 
            className="absolute top-4 right-4 text-gray-500 hover:text-white transition-colors h-8 w-8 flex items-center justify-center bg-gray-800/50 hover:bg-gray-700 rounded-full"
            aria-label="Close"
        >
            &times;
        </button>
        <h2 id="modal-title" className="text-3xl font-bold mb-6 text-white font-brand tracking-wide">Create New Habit</h2>
        
        <form onSubmit={handleSubmit} className="space-y-6">
            {/* Habit Name */}
            <div>
                <label htmlFor="habit-name" className="text-sm font-semibold text-gray-400 mb-2 block">Habit Name</label>
                <input 
                    id="habit-name"
                    type="text" 
                    value={name}
                    onChange={(e) => setName(e.target.value)}
                    placeholder="e.g., Read 10 pages"
                    className="w-full bg-gray-800 border border-gray-700 rounded-lg px-4 py-3 text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-500 transition-all"
                    autoFocus
                />
            </div>

            {/* Habit Type */}
            <div>
                <label className="text-sm font-semibold text-gray-400 mb-2 block">Type</label>
                <div className="flex items-center gap-3">
                    <HabitTypeButton type="check-off" label="Check-off" selected={type === 'check-off'} onClick={setType}>
                        <CheckIcon className="w-6 h-6"/>
                    </HabitTypeButton>
                    <HabitTypeButton type="measurable" label="Measurable" selected={type === 'measurable'} onClick={setType}>
                        <TargetIcon className="w-6 h-6" />
                    </HabitTypeButton>
                    <HabitTypeButton type="timed" label="Timed" selected={type === 'timed'} onClick={setType}>
                        <StopwatchIcon className="w-6 h-6" />
                    </HabitTypeButton>
                </div>
            </div>

            {/* Time Window */}
            <div>
                <label className="flex items-center justify-between cursor-pointer">
                    <span className="text-sm font-semibold text-gray-400 flex items-center gap-2"><ClockIcon className="w-5 h-5"/>Optional Time Window</span>
                    <div className="relative">
                        <input type="checkbox" className="sr-only peer" checked={showTimeWindow} onChange={() => setShowTimeWindow(!showTimeWindow)} />
                        <div className="w-11 h-6 bg-gray-700 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-emerald-600"></div>
                    </div>
                </label>
                {showTimeWindow && (
                    <div className="mt-3 grid grid-cols-2 gap-3">
                        <input type="time" value={startTime} onChange={e => setStartTime(e.target.value)} className="w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-1 focus:ring-emerald-500" />
                        <input type="time" value={endTime} onChange={e => setEndTime(e.target.value)} className="w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-1 focus:ring-emerald-500" />
                    </div>
                )}
            </div>

            {/* Submit Button */}
            <button 
                type="submit" 
                disabled={!name.trim()}
                className="mt-4 w-full flex items-center justify-center gap-2 px-4 py-3 bg-gradient-to-r from-emerald-500 to-green-400 text-black rounded-lg font-bold transition-all transform hover:scale-105 disabled:bg-gray-600 disabled:from-gray-600 disabled:opacity-50 disabled:scale-100"
            >
                <PlusIcon className="w-5 h-5" /> 
                <span>Create Habit</span>
            </button>
        </form>
      </div>
    </div>
  );
};

export default QuickAddModal;