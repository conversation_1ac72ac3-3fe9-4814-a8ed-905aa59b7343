"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RefreshAndSealSessionDataFailureReason = void 0;
var RefreshAndSealSessionDataFailureReason;
(function (RefreshAndSealSessionDataFailureReason) {
    /**
     * @deprecated To be removed in a future major version.
     */
    RefreshAndSealSessionDataFailureReason["INVALID_SESSION_COOKE"] = "invalid_session_cookie";
    RefreshAndSealSessionDataFailureReason["INVALID_SESSION_COOKIE"] = "invalid_session_cookie";
    RefreshAndSealSessionDataFailureReason["NO_SESSION_COOKIE_PROVIDED"] = "no_session_cookie_provided";
    // API OauthErrors for refresh tokens
    RefreshAndSealSessionDataFailureReason["INVALID_GRANT"] = "invalid_grant";
    RefreshAndSealSessionDataFailureReason["MFA_ENROLLMENT"] = "mfa_enrollment";
    RefreshAndSealSessionDataFailureReason["SSO_REQUIRED"] = "sso_required";
    /**
     * @deprecated To be removed in a future major version.
     */
    RefreshAndSealSessionDataFailureReason["ORGANIZATION_NOT_AUTHORIZED"] = "organization_not_authorized";
})(RefreshAndSealSessionDataFailureReason || (exports.RefreshAndSealSessionDataFailureReason = RefreshAndSealSessionDataFailureReason = {}));
