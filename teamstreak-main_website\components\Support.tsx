
import React from 'react';
import Button from './Button';
import { usePages } from '../App';

const Support: React.FC = () => {
    const { openPage } = usePages();
    return (
        <section className="py-16 container mx-auto px-4">
            <div className="text-center mb-12">
                <p className="text-sm uppercase tracking-widest text-gray-400 mb-4">ALWAYS HERE FOR YOU</p>
                <h2 className="text-5xl md:text-7xl font-bold font-unbounded">Dedicated support<br/>for your team</h2>
            </div>
            <div className="bg-[#047857] text-white p-12 lg:p-16 rounded-3xl max-w-5xl mx-auto">
                <div className="flex justify-between items-start mb-8">
                    <div className="bg-[#10B981] text-black text-xs px-3 py-1 rounded-full font-bold">24/7 LIVE CHAT</div>
                </div>
                <p className="text-xl md:text-2xl leading-relaxed max-w-3xl mx-auto text-center mb-8">
                    Whether you're setting up your first accountability circle or integrating with your workflow, our support team is here to help you succeed. We're endlessly patient and ready to help.
                </p>
                <div className="text-center">
                    <p className="mb-4">Have a question?</p>
                    <Button variant="black" onClick={() => openPage('Contact')}>Let's talk</Button>
                </div>
            </div>
        </section>
    );
};

export default Support;