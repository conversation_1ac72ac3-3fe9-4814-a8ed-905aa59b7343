
import { useState } from 'react';
import type { User, ActivityLog, Habit } from '../types';

const initialUsers: User[] = [
  {
    id: 'user-1',
    name: 'You',
    avatar: `https://i.pravatar.cc/150?u=user-1`,
    habits: [
      { id: 'h1', name: 'Drink 8 glasses of water', type: 'check-off', streak: 12, longestStreak: 15, isCompletedToday: true, history: { '2024-07-20': true }, lastCompletedTimestamp: new Date(Date.now() - 1000 * 60 * 123) },
      { id: 'h2', name: 'Read 10 pages', type: 'check-off', streak: 4, longestStreak: 4, isCompletedToday: false, history: {}, timeWindow: { start: '06:00', end: '11:00' } },
      { id: 'h3', name: '30-minute workout', type: 'timed', streak: 0, longestStreak: 8, isCompletedToday: false, history: {} },
      { id: 'h4', name: 'Meditate for 5 minutes', type: 'timed', streak: 23, longestStreak: 23, isCompletedToday: true, history: { '2024-07-20': true }, lastCompletedTimestamp: new Date(Date.now() - 1000 * 60 * 45) },
    ],
  },
  {
    id: 'user-2',
    name: 'Alice',
    avatar: `https://i.pravatar.cc/150?u=user-2`,
    habits: [
      { id: 'h1', name: 'Drink 8 glasses of water', type: 'check-off', streak: 10, longestStreak: 10, isCompletedToday: true, history: {} },
      { id: 'h2', name: 'Read 10 pages', type: 'check-off', streak: 1, longestStreak: 5, isCompletedToday: true, history: {} },
      { id: 'h3', name: '30-minute workout', type: 'timed', streak: 5, longestStreak: 5, isCompletedToday: false, history: {} },
    ],
  },
  {
    id: 'user-3',
    name: 'Bob',
    avatar: `https://i.pravatar.cc/150?u=user-3`,
    habits: [
      { id: 'h1', name: 'Drink 8 glasses of water', type: 'check-off', streak: 3, longestStreak: 20, isCompletedToday: true, history: {} },
      { id: 'h2', name: 'Read 10 pages', type: 'check-off', streak: 0, longestStreak: 2, isCompletedToday: false, history: {} },
      { id: 'h3', name: '30-minute workout', type: 'timed', streak: 0, longestStreak: 1, isCompletedToday: false, history: {} },
      { id: 'h4', name: 'Write journal entry', type: 'check-off', streak: 18, longestStreak: 18, isCompletedToday: true, history: {} },
    ],
  },
    {
    id: 'user-4',
    name: 'Charlie',
    avatar: `https://i.pravatar.cc/150?u=user-4`,
    habits: [
      { id: 'h1', name: 'Drink 8 glasses of water', type: 'check-off', streak: 5, longestStreak: 5, isCompletedToday: true, history: {} },
    ],
  },
];

const initialActivityLog: ActivityLog[] = [
    { id: 'log-4', userId: 'user-4', userName: 'Charlie', userAvatar: `https://i.pravatar.cc/150?u=user-4`, action: 'completed \'Drink 8 glasses of water\'', timestamp: new Date(Date.now() - 1000 * 1), suspect: true },
    { id: 'log-3', userId: 'user-2', userName: 'Alice', userAvatar: `https://i.pravatar.cc/150?u=user-2`, action: 'completed \'Read 10 pages\'', timestamp: new Date(Date.now() - 1000 * 60 * 5) },
    { id: 'log-2', userId: 'user-3', userName: 'Bob', userAvatar: `https://i.pravatar.cc/150?u=user-3`, action: 'completed \'Write journal entry\'', timestamp: new Date(Date.now() - 1000 * 60 * 15) },
    { id: 'log-1', userId: 'user-1', userName: 'You', userAvatar: `https://i.pravatar.cc/150?u=user-1`, action: 'completed \'Drink 8 glasses of water\'', timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2) },
];


export const useMockData = () => {
    const [users, setUsers] = useState<User[]>(initialUsers);
    const [currentUser, setCurrentUser] = useState<User>(initialUsers.find(u => u.id === 'user-1')!);
    const [activityLog, setActivityLog] = useState<ActivityLog[]>(initialActivityLog);

    return {
        users,
        setUsers,
        activityLog,
        setActivityLog,
        currentUser,
        setCurrentUser
    };
};