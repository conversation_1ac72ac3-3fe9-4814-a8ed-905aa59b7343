import React, { useState, useMemo } from 'react';
import type { User } from '../types';

const CalendarView: React.FC<{ user: User }> = ({ user }) => {
  const [selectedDate, setSelectedDate] = useState<string | null>(null);
  const today = new Date();
  const year = today.getFullYear();
  const month = today.getMonth();
  const monthName = today.toLocaleString('default', { month: 'long' });

  const daysInMonth = new Date(year, month + 1, 0).getDate();
  const firstDayOfWeek = new Date(year, month, 1).getDay(); // 0 for Sunday

  const handleDateClick = (dateStr: string) => {
    setSelectedDate(prev => (prev === dateStr ? null : dateStr));
  };

  const completedHabitsOnSelectedDate = useMemo(() => {
    if (!selectedDate || !user.habits) return [];
    return user.habits.filter(habit => habit.history[selectedDate]);
  }, [selectedDate, user.habits]);


  const calendarDays = [];
  for (let i = 0; i < firstDayOfWeek; i++) {
    calendarDays.push(<div key={`empty-${i}`} className="w-9 h-9"></div>);
  }

  for (let day = 1; day <= daysInMonth; day++) {
    const dateStr = `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
    const allHabitsDone = user.habits.length > 0 && user.habits.some(habit => habit.history[dateStr]);
    const isToday = day === today.getDate() && month === today.getMonth() && year === today.getFullYear();
    const isSelected = dateStr === selectedDate;

    calendarDays.push(
      <div 
        key={day} 
        onClick={() => handleDateClick(dateStr)}
        className={`relative w-9 h-9 flex items-center justify-center rounded-full text-sm transition-all cursor-pointer
          ${isToday ? 'bg-emerald-500 text-black font-bold' : 'text-gray-300 hover:bg-gray-800'}
          ${isSelected ? 'ring-2 ring-emerald-500/80' : ''}
        `}
      >
        {day}
        {allHabitsDone && !isSelected && !isToday &&(
          <div className="absolute bottom-1 w-1.5 h-1.5 bg-emerald-500 rounded-full"></div>
        )}
      </div>
    );
  }

  const weekDays = ['S', 'M', 'T', 'W', 'T', 'F', 'S'];

  return (
    <div className="bg-white/5 border border-gray-800/70 p-6 rounded-3xl shadow-lg">
      <h3 className="font-bold text-xl mb-4">{monthName} {year}</h3>
      <div className="grid grid-cols-7 gap-x-1 gap-y-2 text-center">
        {weekDays.map((day, index) => <div key={index} className="text-xs font-bold text-gray-500">{day}</div>)}
        {calendarDays}
      </div>
       {selectedDate && (
        <div className="mt-4 pt-4 border-t border-gray-800/70">
            <h4 className="font-semibold text-md text-gray-300">
                Completed on {new Date(selectedDate + 'T00:00:00').toLocaleDateString('en-US', { month: 'long', day: 'numeric' })}:
            </h4>
            {completedHabitsOnSelectedDate.length > 0 ? (
                <ul className="mt-2 space-y-1.5 text-sm text-gray-400 list-disc list-inside">
                    {completedHabitsOnSelectedDate.map(habit => (
                        <li key={habit.id} className="truncate">{habit.name}</li>
                    ))}
                </ul>
            ) : (
                <p className="mt-2 text-sm text-gray-500">No habits were completed on this day.</p>
            )}
        </div>
    )}
    </div>
  );
};

export default CalendarView;