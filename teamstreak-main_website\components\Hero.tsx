import React, { useEffect, useRef, useCallback } from 'react';
import Button from './Button';
import { useNavigation } from '../App';

const vertexShaderSrc = `
  attribute vec2 a_position;
  void main() {
    gl_Position = vec4(a_position, 0.0, 1.0);
  }
`;

const fragmentShaderSrc = `
  precision highp float;
  uniform vec2 u_resolution;
  uniform float u_time;
  uniform vec2 u_mouse;
  uniform float u_mouse_active;

  // 2D Simplex Noise
  vec3 mod289(vec3 x) { return x - floor(x * (1.0 / 289.0)) * 289.0; }
  vec4 mod289(vec4 x) { return x - floor(x * (1.0 / 289.0)) * 289.0; }
  vec4 permute(vec4 x) { return mod289(((x*34.0)+1.0)*x); }
  vec4 taylorInvSqrt(vec4 r) { return 1.79284291400159 - 0.85373472095314 * r; }

  float snoise(vec3 v) {
    const vec2 C = vec2(1.0/6.0, 1.0/3.0);
    const vec4 D = vec4(0.0, 0.5, 1.0, 2.0);
    vec3 i  = floor(v + dot(v, C.yyy));
    vec3 x0 = v - i + dot(i, C.xxx);
    vec3 g = step(x0.yzx, x0.xyz);
    vec3 l = 1.0 - g;
    vec3 i1 = min(g.xyz, l.zxy);
    vec3 i2 = max(g.xyz, l.zxy);
    vec3 x1 = x0 - i1 + C.xxx;
    vec3 x2 = x0 - i2 + C.yyy;
    vec3 x3 = x0 - D.yyy;
    i = mod289(i);
    vec4 p = permute(permute(permute(
              i.z + vec4(0.0, i1.z, i2.z, 1.0))
            + i.y + vec4(0.0, i1.y, i2.y, 1.0))
            + i.x + vec4(0.0, i1.x, i2.x, 1.0));
    float n_ = 0.142857142857;
    vec3 ns = n_ * D.wyz - D.xzx;
    vec4 j = p - 49.0 * floor(p * ns.z * ns.z);
    vec4 x_ = floor(j * ns.z);
    vec4 y_ = floor(j - 7.0 * x_);
    vec4 x = x_ * ns.x + ns.yyyy;
    vec4 y = y_ * ns.x + ns.yyyy;
    vec4 h = 1.0 - abs(x) - abs(y);
    vec4 b0 = vec4(x.xy, y.xy);
    vec4 b1 = vec4(x.zw, y.zw);
    vec4 s0 = floor(b0)*2.0 + 1.0;
    vec4 s1 = floor(b1)*2.0 + 1.0;
    vec4 sh = -step(h, vec4(0.0));
    vec4 a0 = b0.xzyw + s0.xzyw*sh.xxyy;
    vec4 a1 = b1.xzyw + s1.xzyw*sh.zzww;
    vec3 p0 = vec3(a0.xy,h.x);
    vec3 p1 = vec3(a0.zw,h.y);
    vec3 p2 = vec3(a1.xy,h.z);
    vec3 p3 = vec3(a1.zw,h.w);
    vec4 norm = taylorInvSqrt(vec4(dot(p0,p0), dot(p1,p1), dot(p2,p2), dot(p3,p3)));
    p0 *= norm.x;
    p1 *= norm.y;
    p2 *= norm.z;
    p3 *= norm.w;
    vec4 m = max(0.6 - vec4(dot(x0,x0), dot(x1,x1), dot(x2,x2), dot(x3,x3)), 0.0);
    m = m * m;
    return 42.0 * dot(m*m, vec4(dot(p0,x0), dot(p1,x1), dot(p2,x2), dot(p3,x3)));
  }

  // Fractional Brownian Motion
  float fbm(vec3 p) {
    float value = 0.0;
    float amplitude = 0.5;
    for (int i = 0; i < 6; i++) {
      value += amplitude * snoise(p);
      p *= 2.0;
      amplitude *= 0.5;
    }
    return value;
  }

  void main() {
    vec2 st = gl_FragCoord.xy/u_resolution.xy;
    st.x *= u_resolution.x/u_resolution.y;

    vec2 mouse_uv = u_mouse / u_resolution.xy;
    mouse_uv.y = 1.0 - mouse_uv.y;
    mouse_uv.x *= u_resolution.x/u_resolution.y;
    
    vec3 color = vec3(0.062, 0.062, 0.062); // #101010
    vec3 accentColor = vec3(0.023, 0.47, 0.341); // #067857, darker shade for veins
    vec3 highlightColor = vec3(0.0627, 0.725, 0.505); // #10B981 for highlight

    float t = u_time * 0.1;

    float mouse_dist = distance(st, mouse_uv);
    float mouse_effect = smoothstep(0.25, 0.0, mouse_dist) * u_mouse_active;

    // Distort space for a liquid effect
    vec2 offset = vec2(fbm(vec3(st * 2.5, t)), fbm(vec3(st * 2.5 + 5.2, t)));
    offset *= (0.05 + mouse_effect * 0.1);
    
    // Base pattern
    float n = fbm(vec3((st + offset) * 1.5, t));

    // Veins effect
    float veins = smoothstep(0.4, 0.41, n) - smoothstep(0.45, 0.46, n);
    veins += smoothstep(0.5, 0.51, n) - smoothstep(0.55, 0.56, n);
    
    color = mix(color, accentColor, veins);

    // Mouse highlight
    color = mix(color, highlightColor, mouse_effect * 0.5);

    gl_FragColor = vec4(color, 1.0);
  }
`;


const Hero: React.FC = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const { navigateToPlayground } = useNavigation();
  const mousePos = useRef({ x: 0, y: 0 });
  const mouseActive = useRef(false);

  const handleMouseMove = useCallback((event: React.MouseEvent<HTMLElement>) => {
      const rect = canvasRef.current?.getBoundingClientRect();
      if (rect) {
          mousePos.current = { x: event.clientX - rect.left, y: event.clientY - rect.top };
      }
  }, []);

  const handleMouseEnter = useCallback(() => {
      mouseActive.current = true;
  }, []);

  const handleMouseLeave = useCallback(() => {
      mouseActive.current = false;
  }, []);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const gl = canvas.getContext('webgl', { antialias: true });
    if (!gl) {
        console.error("WebGL not supported");
        return;
    }

    const createShader = (gl: WebGLRenderingContext, type: number, source: string) => {
        const shader = gl.createShader(type);
        if(!shader) return null;
        gl.shaderSource(shader, source);
        gl.compileShader(shader);
        if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS)) {
            console.error('An error occurred compiling the shaders: ' + gl.getShaderInfoLog(shader));
            gl.deleteShader(shader);
            return null;
        }
        return shader;
    };

    const vertexShader = createShader(gl, gl.VERTEX_SHADER, vertexShaderSrc);
    const fragmentShader = createShader(gl, gl.FRAGMENT_SHADER, fragmentShaderSrc);

    if (!vertexShader || !fragmentShader) return;

    const program = gl.createProgram();
    if(!program) return;
    gl.attachShader(program, vertexShader);
    gl.attachShader(program, fragmentShader);
    gl.linkProgram(program);

    if (!gl.getProgramParameter(program, gl.LINK_STATUS)) {
        console.error('Unable to initialize the shader program: ' + gl.getProgramInfoLog(program));
        return;
    }
    gl.useProgram(program);

    const positionBuffer = gl.createBuffer();
    gl.bindBuffer(gl.ARRAY_BUFFER, positionBuffer);
    const positions = [-1, 1, 1, 1, -1, -1, 1, -1];
    gl.bufferData(gl.ARRAY_BUFFER, new Float32Array(positions), gl.STATIC_DRAW);

    const positionAttributeLocation = gl.getAttribLocation(program, "a_position");
    gl.enableVertexAttribArray(positionAttributeLocation);
    gl.vertexAttribPointer(positionAttributeLocation, 2, gl.FLOAT, false, 0, 0);

    const resolutionUniformLocation = gl.getUniformLocation(program, "u_resolution");
    const timeUniformLocation = gl.getUniformLocation(program, "u_time");
    const mouseUniformLocation = gl.getUniformLocation(program, "u_mouse");
    const mouseActiveUniformLocation = gl.getUniformLocation(program, "u_mouse_active");
    
    let animationFrameId: number;
    const render = (time: number) => {
        time *= 0.001; // convert to seconds
        
        if (canvas.width !== canvas.clientWidth || canvas.height !== canvas.clientHeight) {
            canvas.width = canvas.clientWidth;
            canvas.height = canvas.clientHeight;
            gl.viewport(0, 0, gl.canvas.width, gl.canvas.height);
        }

        gl.uniform2f(resolutionUniformLocation, gl.canvas.width, gl.canvas.height);
        gl.uniform1f(timeUniformLocation, time);
        gl.uniform2f(mouseUniformLocation, mousePos.current.x, mousePos.current.y);
        gl.uniform1f(mouseActiveUniformLocation, mouseActive.current ? 1.0 : 0.0);
        
        gl.drawArrays(gl.TRIANGLE_STRIP, 0, 4);

        animationFrameId = requestAnimationFrame(render);
    };

    requestAnimationFrame(render);

    return () => {
      cancelAnimationFrame(animationFrameId);
    };
  }, []);

  return (
    <section 
        className="bg-[#101010] text-white min-h-screen flex flex-col justify-center pt-24 pb-16 rounded-b-3xl relative overflow-hidden"
        onMouseMove={handleMouseMove}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
    >
       <canvas ref={canvasRef} className="absolute top-0 left-0 w-full h-full z-0" />
       <div className="container mx-auto px-6 text-center relative z-10">
        <h1 className="font-unbounded text-6xl md:text-8xl lg:text-9xl font-black leading-none mb-4 tracking-tighter">TeamStreak</h1>
        <p className="text-3xl md:text-5xl font-bold max-w-4xl mx-auto mb-8 font-unbounded leading-tight">
          Build Habits, Together.
        </p>
        <p className="text-lg md:text-xl max-w-2xl mx-auto mb-10 text-gray-400">
          The shared habit tracker for high-performing teams. Create accountability circles, track daily progress, and build unstoppable momentum with social motivation.
        </p>
        <Button variant="yellow" className="px-8 py-4 text-lg" onClick={navigateToPlayground}>Start tracking</Button>
      </div>
    </section>
  );
};

export default Hero;
