"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.serializeAuthenticateWithMagicAuthOptions = void 0;
const serializeAuthenticateWithMagicAuthOptions = (options) => ({
    grant_type: 'urn:workos:oauth:grant-type:magic-auth:code',
    client_id: options.clientId,
    client_secret: options.clientSecret,
    code: options.code,
    email: options.email,
    invitation_token: options.invitationToken,
    link_authorization_code: options.linkAuthorizationCode,
    ip_address: options.ipAddress,
    user_agent: options.userAgent,
});
exports.serializeAuthenticateWithMagicAuthOptions = serializeAuthenticateWithMagicAuthOptions;
