import { Totp, TotpResponse, TotpWithSecrets, TotpWithSecretsResponse } from '../../mfa/interfaces/totp.interface';
export interface Factor {
    object: 'authentication_factor';
    id: string;
    createdAt: string;
    updatedAt: string;
    type: 'totp';
    totp: Totp;
    userId: string;
}
export interface FactorWithSecrets {
    object: 'authentication_factor';
    id: string;
    createdAt: string;
    updatedAt: string;
    type: 'totp';
    totp: TotpWithSecrets;
    userId: string;
}
export interface FactorResponse {
    object: 'authentication_factor';
    id: string;
    created_at: string;
    updated_at: string;
    type: 'totp';
    totp: TotpResponse;
    user_id: string;
}
export interface FactorWithSecretsResponse {
    object: 'authentication_factor';
    id: string;
    created_at: string;
    updated_at: string;
    type: 'totp';
    totp: TotpWithSecretsResponse;
    user_id: string;
}
