
import React from 'react';
import type { ActivityLog } from '../types';

function formatTimestamp(date: Date): string {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    const targetDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());

    if (targetDate.getTime() === today.getTime()) {
        return date.toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit', hour12: true });
    }
    if (targetDate.getTime() === yesterday.getTime()) {
        return "Yesterday";
    }
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
}


const ActivityFeed: React.FC<{ logs: ActivityLog[] }> = ({ logs }) => {
  return (
    <div className="bg-white/5 border border-gray-800/70 p-6 rounded-3xl shadow-lg">
      <h3 className="font-bold text-xl mb-4">Activity Feed</h3>
      <ul className="space-y-1 max-h-96 overflow-y-auto pr-2 -mr-2">
        {logs.map(log => (
          <li key={log.id} className="flex items-start gap-3 py-4 border-b border-gray-800/70 last:border-b-0">
            <img src={log.userAvatar} alt={log.userName} className="w-9 h-9 rounded-full mt-1 border-2 border-gray-700" />
            <div className="flex-1">
              <div className="flex items-center flex-wrap">
                <p className="text-sm text-gray-300">
                  <span className="font-semibold text-white">{log.userName}</span> {log.action}
                </p>
                {log.suspect && (
                  <span 
                    className="ml-2 mt-0.5 text-xs bg-red-500/10 text-red-400 px-2 py-0.5 rounded-full cursor-help font-semibold"
                    title="Multiple actions in short time."
                  >
                    Suspect
                  </span>
                )}
              </div>
              <p className="text-xs text-gray-500 mt-0.5">{formatTimestamp(log.timestamp)}</p>
              <div className="mt-2.5 flex items-center gap-2">
                  <button className="px-2.5 py-1 bg-gray-800/70 hover:bg-gray-700 rounded-full text-xs transition-colors">👍</button>
                  <button className="px-2.5 py-1 bg-gray-800/70 hover:bg-gray-700 rounded-full text-xs transition-colors">👏</button>
                  <button className="px-2.5 py-1 bg-gray-800/70 hover:bg-gray-700 rounded-full text-xs transition-colors">🔥</button>
                  <a href="#" className="text-xs text-gray-400 hover:text-white ml-2 transition-colors">Reply</a>
              </div>
            </div>
          </li>
        ))}
        {logs.length === 0 && <p className="text-gray-500 text-sm py-8 text-center">No activity yet. Complete a habit to get started!</p>}
      </ul>
    </div>
  );
};

export default ActivityFeed;
