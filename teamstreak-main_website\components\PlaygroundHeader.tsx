import React from 'react';
import type { User } from '../types';
import { ChevronDownIcon, TeamStreakIcon } from './icons';

interface HeaderProps {
    user: User;
}

const Header: React.FC<HeaderProps> = ({ user }) => {
    return (
        <header className="sticky top-0 z-10 bg-black/50 backdrop-blur-md border-b border-gray-800">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8 lg:px-12">
                <div className="flex items-center justify-between h-20">
                    <div className="flex items-center gap-4">
                        {/* Org Switcher */}
                        <button className="flex items-center gap-2 px-3 py-2 bg-gray-900/50 hover:bg-gray-800 rounded-lg transition-colors">
                            <TeamStreakIcon className="w-5 h-5 text-emerald-500" />
                            <span className="font-bold text-white">TeamStreak</span>
                            <ChevronDownIcon className="w-4 h-4 text-gray-400" />
                        </button>
                        <span className="text-gray-700">/</span>
                        {/* Group Selector */}
                        <button className="flex items-center gap-2 px-3 py-2 bg-gray-900/50 hover:bg-gray-800 rounded-lg transition-colors">
                            <span className="font-semibold text-gray-200">Q3 Engineering</span>
                            <ChevronDownIcon className="w-4 h-4 text-gray-400" />
                        </button>
                    </div>

                    <div className="flex items-center gap-6">
                        <div className="text-right hidden sm:block">
                            <p className="font-semibold text-white text-sm">{new Date().toLocaleDateString('en-US', { weekday: 'long' })}</p>
                            <p className="text-xs text-gray-400">{new Date().toLocaleDateString('en-US', { month: 'long', day: 'numeric' })}</p>
                        </div>
                        {/* Profile Menu */}
                        <button className="flex items-center gap-2">
                             <img src={user.avatar} alt={user.name} className="w-10 h-10 rounded-full border-2 border-gray-700" />
                        </button>
                    </div>
                </div>
            </div>
        </header>
    );
};

export default Header;