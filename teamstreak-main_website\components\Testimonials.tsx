
import React from 'react';

interface TestimonialCardProps {
    avatar: string;
    name: string;
    handle: string;
    children: React.ReactNode;
}

const TestimonialCard: React.FC<TestimonialCardProps> = ({ avatar, name, handle, children }) => (
    <div className="bg-[#047857] text-white p-6 rounded-3xl flex flex-col h-full">
        <div className="flex items-center mb-4">
            <img src={avatar} alt={name} className="w-12 h-12 rounded-full mr-4 object-cover" />
            <div>
                <p className="font-bold">{name}</p>
                <p className="text-sm text-emerald-200">{handle}</p>
            </div>
        </div>
        <p className="text-white leading-relaxed flex-grow">{children}</p>
    </div>
);


const Testimonials: React.FC = () => {
    return (
        <section className="py-16 container mx-auto px-4">
            <div className="text-center mb-12 max-w-4xl mx-auto">
                <p className="text-sm uppercase tracking-widest text-gray-400 mb-4">TRUSTED BY TEAMS WORLDWIDE</p>
                <h2 className="text-5xl md:text-7xl font-bold font-unbounded">See what leaders are saying</h2>
                <p className="mt-6 text-lg text-gray-300">A product is only as strong as its community. We're helping teams build better habits, foster camaraderie, and achieve their goals.</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <TestimonialCard 
                    avatar="https://images.pexels.com/photos/774909/pexels-photo-774909.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2" 
                    name="Sarah K." 
                    handle="Engineering Manager, TechCorp">
                    "TeamStreak has been a game-changer for our remote team's morale. The daily check-ins and friendly competition have brought us closer, even though we're miles apart. Engagement is through the roof."
                </TestimonialCard>
                 <TestimonialCard 
                    avatar="https://images.pexels.com/photos/91227/pexels-photo-91227.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2"
                    name="Michael B." 
                    handle="CEO, Innovate Solutions">
                    "I was looking for a way to encourage healthy habits without being intrusive. TeamStreak was the perfect solution. It's simple, fun, and has had a noticeable impact on our team's overall productivity and well-being."
                </TestimonialCard>
                 <TestimonialCard 
                    avatar="https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2"
                    name="Jessica L." 
                    handle="Product Lead, DesignHub">
                    "The accountability circles are brilliant. We use them for everything from daily stand-up goals to fitness challenges. It's the best tool we've found for building and maintaining positive momentum as a team."
                </TestimonialCard>
            </div>
            
            <div className="mt-16 flex justify-center items-center gap-4">
                <div className="w-24 h-1 bg-gray-700 rounded-full"></div>
                <div className="flex items-center gap-3">
                    <span className="bg-[#047857] text-white w-8 h-8 flex items-center justify-center rounded-full font-bold text-sm">01</span>
                    <span className="bg-gray-800 text-gray-400 w-8 h-8 flex items-center justify-center rounded-full font-bold text-sm">02</span>
                    <span className="bg-gray-800 text-gray-400 w-8 h-8 flex items-center justify-center rounded-full font-bold text-sm">03</span>
                </div>
                <div className="w-24 h-1 bg-gray-700 rounded-full"></div>
            </div>
        </section>
    );
};

export default Testimonials;