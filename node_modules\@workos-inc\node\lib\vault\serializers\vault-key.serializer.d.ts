import { CreateDataKeyResponse } from '../interfaces/key/create-data-key.interface';
import { DecryptDataKeyResponse } from '../interfaces/key/decrypt-data-key.interface';
import { DataKey, DataKeyPair } from '../interfaces/key.interface';
export declare const deserializeCreateDataKeyResponse: (key: CreateDataKeyResponse) => DataKeyPair;
export declare const deserializeDecryptDataKeyResponse: (key: DecryptDataKeyResponse) => DataKey;
