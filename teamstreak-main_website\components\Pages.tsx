import React from 'react';
import { usePages } from '../App';
import Button from './Button';
import { SharedGoalsIcon, StreakTrackingIcon, PrivateCirclesIcon, DailyCheckinsIcon, WorkOSIcon } from './Icons';

const PageWrapper: React.FC<{title: string, children: React.ReactNode, subTitle?: string}> = ({title, children, subTitle}) => (
    <div className="animate-fade-in-up">
        <h1 className="text-5xl md:text-7xl font-unbounded font-bold text-[#10B981]">{title}</h1>
        {subTitle && <p className="mt-4 text-xl text-gray-300 max-w-3xl">{subTitle}</p>}
        <div className="mt-12">
            {children}
        </div>
    </div>
);

const FeatureCard: React.FC<{icon: React.ReactNode, title: string, children: React.ReactNode, delay: number}> = ({ icon, title, children, delay }) => (
    <div className="bg-black/20 p-8 rounded-2xl border border-white/10 animate-fade-in-up opacity-0" style={{ animationDelay: `${delay}s` }}>
        <div className="text-[#10B981] mb-4">
            {icon}
        </div>
        <h3 className="text-2xl font-bold font-unbounded text-white mb-2">{title}</h3>
        <p className="text-gray-400 leading-relaxed">{children}</p>
    </div>
);

export const FeaturesPage: React.FC = () => (
    <PageWrapper title="Features" subTitle="Discover the powerful features that make TeamStreak the ultimate habit tracker for teams, designed to foster accountability and motivation.">
       <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <FeatureCard icon={<SharedGoalsIcon className="w-10 h-10" />} title="Shared Leaderboards" delay={0}>
                Create friendly competition and track progress collectively with real-time leaderboards that update as your team checks in.
            </FeatureCard>
             <FeatureCard icon={<StreakTrackingIcon className="w-10 h-10" />} title="Real-time Streaks" delay={0.1}>
                Visualize individual and team momentum. Celebrate milestones together and never break the chain!
            </FeatureCard>
             <FeatureCard icon={<PrivateCirclesIcon className="w-10 h-10" />} title="Private Circles" delay={0.2}>
                Form small, dedicated groups for specific habits or projects to provide focused support and accountability.
            </FeatureCard>
             <FeatureCard icon={<DailyCheckinsIcon className="w-10 h-10" />} title="Daily Check-ins" delay={0.3}>
                Keep everyone on track with simple, one-click daily check-ins and configurable reminders for every circle.
            </FeatureCard>
             <FeatureCard icon={<WorkOSIcon className="w-10 h-10" />} title="WorkOS Integration" delay={0.4}>
                Log in securely with your existing work accounts (like Google and Microsoft) for easy, secure onboarding.
            </FeatureCard>
       </div>
    </PageWrapper>
);

export const PricingPage: React.FC = () => (
    <PageWrapper title="Pricing" subTitle="Simple, transparent pricing that scales with your team. Get started for free, and upgrade when you're ready.">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 text-white">
            <div className="bg-black/20 p-8 rounded-2xl border border-white/10">
                <h3 className="text-2xl font-bold font-unbounded">Starter</h3>
                <p className="text-4xl font-black font-unbounded my-4">Free</p>
                <p className="text-gray-400">For small teams getting started.</p>
                <ul className="my-6 space-y-2 list-disc pl-5 text-gray-300">
                    <li>Up to 5 users</li>
                    <li>1 Accountability Circle</li>
                    <li>Basic Habit Tracking</li>
                </ul>
                <Button variant="yellow" className="w-full mt-4">Get Started</Button>
            </div>
            <div className="bg-black/20 p-8 rounded-2xl border border-white/10">
                <h3 className="text-2xl font-bold font-unbounded">Pro</h3>
                <p className="text-4xl font-black font-unbounded my-4">$10 <span className="text-lg font-normal">/ user / month</span></p>
                <p className="text-gray-400">For growing teams that need more.</p>
                <ul className="my-6 space-y-2 list-disc pl-5 text-gray-300">
                    <li>Unlimited users</li>
                    <li>Unlimited Circles</li>
                    <li>Advanced Analytics</li>
                    <li>Priority Support</li>
                </ul>
                <Button variant="yellow" className="w-full mt-4">Go Pro</Button>
            </div>
        </div>
    </PageWrapper>
);

export const HowItWorksPage: React.FC = () => (
    <PageWrapper title="How It Works" subTitle="Getting started with TeamStreak is as easy as 1-2-3.">
        <div className="prose prose-invert prose-lg max-w-none">
            <ol className="list-decimal pl-6 mt-4 space-y-4">
                <li><strong>Create a Circle:</strong> The team leader or manager creates an "accountability circle" for a specific habit (e.g., "Daily Code Commits", "Morning Workouts").</li>
                <li><strong>Invite Your Team:</strong> Easily invite team members to the circle using their work email. They can join with a single click.</li>
                <li><strong>Track & Compete:</strong> Each day, members check in to mark their habit as complete. Progress is updated on a live leaderboard, fueling motivation and friendly competition.</li>
            </ol>
        </div>
    </PageWrapper>
);

export const SecurityPage: React.FC = () => (
    <PageWrapper title="Security" subTitle="Your team's data and privacy are our top priority. We employ industry-standard security practices to keep your information safe.">
       <div className="prose prose-invert prose-lg max-w-none">
            <ul>
                <li><strong>Data Encryption:</strong> All data is encrypted at rest and in transit using AES-256 and TLS 1.2/1.3.</li>
                <li><strong>WorkOS Integration:</strong> We leverage WorkOS for secure, enterprise-grade authentication. We never store your passwords.</li>
                <li><strong>Privacy First:</strong> We will never sell your data. Your team's habit data is private to your organization and can be deleted upon request.</li>
            </ul>
        </div>
    </PageWrapper>
);


export const AboutPage: React.FC = () => (
    <PageWrapper title="About Us" subTitle="We are a team of builders, creators, and habit-nerds passionate about helping teams unlock their full potential.">
        <div className="prose prose-invert prose-lg max-w-none">
            <p>We believe that small, consistent actions, when done together, lead to massive results. TeamStreak was born from our own need to stay connected and motivated as a remote team. Our mission is to build simple, beautiful tools that foster camaraderie and drive collective success.</p>
        </div>
    </PageWrapper>
);


export const ContactPage: React.FC = () => (
    <PageWrapper title="Contact Us" subTitle="Have a question, feedback, or just want to say hello? We'd love to hear from you.">
       <div className="prose prose-invert prose-lg max-w-none">
             <p>For support, sales, and general inquiries, you can reach our team at <a href="mailto:<EMAIL>" className="font-bold text-[#10B981] hover:underline"><EMAIL></a>. We'll get back to you as soon as we can!</p>
       </div>
    </PageWrapper>
);