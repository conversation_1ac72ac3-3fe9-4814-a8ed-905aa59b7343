"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deserializeUser = void 0;
const deserializeUser = (user) => {
    var _a, _b;
    return ({
        object: user.object,
        id: user.id,
        email: user.email,
        emailVerified: user.email_verified,
        firstName: user.first_name,
        profilePictureUrl: user.profile_picture_url,
        lastName: user.last_name,
        lastSignInAt: user.last_sign_in_at,
        createdAt: user.created_at,
        updatedAt: user.updated_at,
        externalId: (_a = user.external_id) !== null && _a !== void 0 ? _a : null,
        metadata: (_b = user.metadata) !== null && _b !== void 0 ? _b : {},
    });
};
exports.deserializeUser = deserializeUser;
