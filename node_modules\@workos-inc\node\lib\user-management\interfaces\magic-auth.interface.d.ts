export interface MagicAuth {
    object: 'magic_auth';
    id: string;
    userId: string;
    email: string;
    expiresAt: string;
    code: string;
    createdAt: string;
    updatedAt: string;
}
export interface MagicAuthEvent {
    object: 'magic_auth';
    id: string;
    userId: string;
    email: string;
    expiresAt: string;
    createdAt: string;
    updatedAt: string;
}
export interface MagicAuthResponse {
    object: 'magic_auth';
    id: string;
    user_id: string;
    email: string;
    expires_at: string;
    code: string;
    created_at: string;
    updated_at: string;
}
export interface MagicAuthEventResponse {
    object: 'magic_auth';
    id: string;
    user_id: string;
    email: string;
    expires_at: string;
    created_at: string;
    updated_at: string;
}
