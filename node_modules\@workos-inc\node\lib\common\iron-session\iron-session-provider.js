"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.IronSessionProvider = void 0;
/**
 * Interface encapsulating the sealData/unsealData methods for separate iron-session implementations.
 *
 * This allows for different implementations of the iron-session library to be used in
 * worker/edge vs. regular web environments, which is required because of the different crypto APIs available.
 * Once we drop support for Node 16 and upgrade to iron-session 8+, we can remove this abstraction as iron-session 8+
 * handles this on its own.
 */
class IronSessionProvider {
}
exports.IronSessionProvider = IronSessionProvider;
