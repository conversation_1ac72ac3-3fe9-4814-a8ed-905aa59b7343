"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FetchError = void 0;
class FetchError extends Error {
    constructor({ message, response, }) {
        super(message);
        this.name = 'FetchError';
        this.message = 'The request could not be completed.';
        this.message = message;
        this.response = response;
    }
}
exports.FetchError = FetchError;
