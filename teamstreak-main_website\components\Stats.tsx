
import React from 'react';
import { StarIcon } from './Icons';

const StatItem: React.FC<{ value: string; label: string; icon?: React.ReactNode }> = ({ value, label, icon }) => (
  <div className="text-center">
    <div className="flex items-center justify-center gap-2 text-3xl md:text-4xl font-bold font-unbounded">
      {icon}
      <span>{value}</span>
    </div>
    <p className="text-xs uppercase tracking-widest mt-1">{label}</p>
  </div>
);

const Stats: React.FC = () => {
  return (
    <div className="bg-[#047857] text-white py-10 my-16 mx-4 rounded-3xl">
      <div className="container mx-auto px-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <StatItem value="4.9/5" label="Rated by high-performing teams" icon={<StarIcon className="w-8 h-8" />} />
          <StatItem value="10,000+" label="Teams Onboarded" />
          <StatItem value="5M+" label="Habits Tracked Together" />
        </div>
      </div>
    </div>
  );
};

export default Stats;