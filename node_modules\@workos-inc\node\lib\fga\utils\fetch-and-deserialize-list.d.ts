import { WorkOS } from '../../workos';
import { FGAList } from '../interfaces/list.interface';
import { QueryRequestOptions } from '../interfaces';
import { PaginationOptions } from '../../common/interfaces';
export declare const fetchAndDeserializeFGAList: <T, U>(workos: WorkOS, endpoint: string, deserializeFn: (data: T) => U, options?: PaginationOptions, requestOptions?: QueryRequestOptions) => Promise<FGAList<U>>;
