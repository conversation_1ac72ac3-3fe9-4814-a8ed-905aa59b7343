import React, { useState, useEffect } from 'react';
import { TeamStreakIcon, ChevronDownIcon, MenuIcon, CloseIcon } from './Icons';
import Button from './Button';
import { usePages, useNavigation } from '../App';

const NavLink: React.FC<{ children: React.ReactNode; pageKey: string; className?: string }> = ({ children, pageKey, className='' }) => {
  const { openPage } = usePages();
  return (
    <button onClick={() => openPage(pageKey)} className={`px-4 py-2 rounded-full text-white hover:bg-white/10 transition-colors duration-200 ${className}`}>
      <span>{children}</span>
    </button>
  );
};

const MobileMenu: React.FC<{ onClose: () => void }> = ({ onClose }) => {
  const { openPage } = usePages();

  const handleOpen = (pageKey: string) => {
    openPage(pageKey);
    onClose();
  }

  return (
    <div className="fixed inset-0 bg-black/90 backdrop-blur-sm z-50 flex flex-col items-center justify-center lg:hidden">
      <button onClick={onClose} className="absolute top-6 right-6 text-white">
        <CloseIcon className="w-8 h-8" />
      </button>
      <nav className="flex flex-col items-center gap-8">
        <button onClick={() => handleOpen('Features')} className="text-2xl flex items-center gap-2 text-white hover:text-gray-300 transition-colors">Features</button>
        <button onClick={() => handleOpen('Pricing')} className="text-2xl text-white hover:text-gray-300 transition-colors">Pricing</button>
        <button onClick={() => handleOpen('How It Works')} className="text-2xl text-white hover:text-gray-300 transition-colors">How It Works</button>
      </nav>
    </div>
  );
};


const Header: React.FC = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const { navigateToPlayground } = useNavigation();

  useEffect(() => {
    if (isMenuOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }
  }, [isMenuOpen]);

  return (
    <header className="absolute top-0 left-0 right-0 z-40">
      <div className="container mx-auto px-6 py-4 flex justify-between items-center">
        <a href="#" className="flex items-center gap-3">
          <TeamStreakIcon className="text-white w-8 h-8" />
          <span className="text-xl font-bold font-unbounded">TeamStreak</span>
        </a>

        <nav className="hidden lg:flex items-center gap-2 bg-black/20 backdrop-blur-sm px-6 py-2 rounded-full border border-white/10">
          <NavLink pageKey="Features">Features</NavLink>
          <NavLink pageKey="Pricing">Pricing</NavLink>
          <NavLink pageKey="How It Works">How It Works</NavLink>
        </nav>
        
        <div className="hidden lg:block">
            <Button variant="yellow" onClick={navigateToPlayground}>Start tracking</Button>
        </div>

        <button className="lg:hidden text-white" onClick={() => setIsMenuOpen(true)}>
          <MenuIcon className="w-8 h-8" />
        </button>
      </div>
      {isMenuOpen && <MobileMenu onClose={() => setIsMenuOpen(false)} />}
    </header>
  );
};

export default Header;