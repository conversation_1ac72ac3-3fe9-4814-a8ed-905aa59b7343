"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deserializeMagicAuthEvent = exports.deserializeMagicAuth = void 0;
const deserializeMagicAuth = (magicAuth) => ({
    object: magicAuth.object,
    id: magicAuth.id,
    userId: magicAuth.user_id,
    email: magicAuth.email,
    expiresAt: magicAuth.expires_at,
    code: magicAuth.code,
    createdAt: magicAuth.created_at,
    updatedAt: magicAuth.updated_at,
});
exports.deserializeMagicAuth = deserializeMagicAuth;
const deserializeMagicAuthEvent = (magicAuth) => ({
    object: magicAuth.object,
    id: magicAuth.id,
    userId: magicAuth.user_id,
    email: magicAuth.email,
    expiresAt: magicAuth.expires_at,
    createdAt: magicAuth.created_at,
    updatedAt: magicAuth.updated_at,
});
exports.deserializeMagicAuthEvent = deserializeMagicAuthEvent;
