
import React, { useState } from 'react';
import type { User, Habit } from '../types';
import { FlameIcon, CheckIcon, PlusIcon, SpinnerIcon } from './icons';

const formatTime = (date?: Date): string => {
  if (!date) return '';
  return date.toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit', hour12: true });
}

interface HabitItemProps {
  habit: Habit;
  onToggle: (habitId: string, isCompleted: boolean) => void;
  isCelebrating: boolean;
}

const HabitItem: React.FC<HabitItemProps> = ({ habit, onToggle, isCelebrating }) => {
  const [isPending, setIsPending] = useState(false);

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setIsPending(true);
    onToggle(habit.id, e.target.checked);
    setTimeout(() => setIsPending(false), 1000); // Simulate server confirmation
  };

  const containerClasses = `relative group transition-all duration-300 rounded-lg ${habit.isCompletedToday ? 'opacity-60' : ''}`;
  
  const contentClasses = `flex items-center justify-between p-4 rounded-lg transition-colors ${
    habit.isCompletedToday 
      ? 'bg-gray-900/50' 
      : 'bg-gray-800/50 group-hover:bg-gray-800 border border-gray-700/50 group-hover:border-gray-600'
  }`;

  return (
    <div className={containerClasses}>
      <div className={contentClasses}>
        <div className="flex-1">
          <div className="flex items-center gap-4">
            <label className="relative flex items-center justify-center w-7 h-7 cursor-pointer">
              <input
                type="checkbox"
                checked={habit.isCompletedToday}
                onChange={handleCheckboxChange}
                className="peer appearance-none w-full h-full rounded-md border-2 border-gray-600 group-hover:border-gray-500 checked:bg-emerald-500 checked:border-emerald-500 transition-all"
                aria-label={habit.name}
                disabled={isPending}
              />
              <CheckIcon className={`absolute w-4 h-4 text-black transition-opacity ${habit.isCompletedToday && !isPending ? 'opacity-100' : 'opacity-0'}`} />
              {isPending && <SpinnerIcon className="absolute w-5 h-5 text-emerald-500" />}
            </label>
            <span className={`text-lg transition-colors ${habit.isCompletedToday ? 'text-gray-500 line-through' : 'text-white'}`}>
              {habit.name}
            </span>
          </div>
          <div className="pl-11 mt-1.5 flex items-center gap-4 text-xs text-gray-500">
              {habit.isCompletedToday && habit.lastCompletedTimestamp && <span>Last checked: {formatTime(habit.lastCompletedTimestamp)}</span>}
              {habit.timeWindow && <span className="text-purple-400 font-semibold">Window: {habit.timeWindow.start} - {habit.timeWindow.end}</span>}
          </div>
        </div>
        <div className="flex items-center gap-3 bg-black/20 text-emerald-500 border border-emerald-500/30 px-3 py-1 rounded-full">
          <div className="flex items-baseline gap-1">
            <span className="font-stats text-2xl tracking-wider [text-shadow:0_0_8px_theme(colors.emerald.500/0.7)]">{habit.streak}</span>
            <span className="text-xs text-gray-500 font-mono">/ {habit.longestStreak || habit.streak}</span>
          </div>
          <FlameIcon className="w-4 h-4" />
        </div>
      </div>
    </div>
  );
};

const SuggestedHabit: React.FC<{name: string, onAdd: () => void}> = ({ name, onAdd }) => (
    <button 
        onClick={onAdd}
        className="w-full text-left p-3 bg-gray-800/50 hover:bg-gray-700 rounded-lg transition-colors flex items-center gap-3"
    >
        <PlusIcon className="w-5 h-5 text-emerald-500"/>
        <span className="text-gray-300">{name}</span>
    </button>
)

interface HabitTrackerProps {
  user: User;
  onToggleHabit: (habitId: string, isCompleted: boolean) => void;
  celebration: { habitId: string; streak: number } | null;
  onQuickAdd: () => void;
  onAddHabit: (habit: Partial<Habit>) => void;
}

const HabitTracker: React.FC<HabitTrackerProps> = ({ user, onToggleHabit, celebration, onQuickAdd, onAddHabit }) => {
  const isPerfectDay = user.habits.length > 0 && user.habits.every(h => h.isCompletedToday);

  const containerClasses = `
      bg-white/5 border border-gray-800/70 p-6 rounded-3xl shadow-lg transition-all duration-500
      ${isPerfectDay ? 'border-emerald-500/50 shadow-emerald-500/20 shadow-[0_0_20px_4px]' : ''}
  `;

  return (
    <div className={containerClasses}>
      <div className="flex justify-between items-center mb-6">
        <h2 className="font-brand text-5xl text-white">Today's Habits</h2>
        <button onClick={onQuickAdd} className="flex items-center gap-2 px-4 py-2 bg-emerald-500 text-black rounded-lg font-bold hover:bg-emerald-400 transition-transform hover:scale-105">
          <PlusIcon className="w-5 h-5" />
          <span>Quick Add</span>
        </button>
      </div>
      <div className="space-y-3">
        {user.habits.length > 0 ? (
          user.habits.map((habit) => (
            <HabitItem 
                key={habit.id} 
                habit={habit} 
                onToggle={onToggleHabit}
                isCelebrating={celebration?.habitId === habit.id}
            />
          ))
        ) : (
          <div className="text-center py-10 px-6">
            <h3 className="text-xl font-bold text-white">Start Your Streak</h3>
            <p className="text-gray-400 mt-2 mb-6">Add one habit and invite one person to try it together.</p>
            <div className="mt-8 border-t border-gray-800 pt-6">
                <h4 className="text-sm font-semibold text-gray-500 uppercase tracking-wider mb-3">Start with a suggestion:</h4>
                <div className="space-y-2 max-w-md mx-auto">
                    <SuggestedHabit name="Drink 8 glasses of water" onAdd={() => onAddHabit({ name: 'Drink 8 glasses of water', type: 'check-off' })} />
                    <SuggestedHabit name="Read 10 pages" onAdd={() => onAddHabit({ name: 'Read 10 pages', type: 'check-off' })} />
                    <SuggestedHabit name="30-minute workout" onAdd={() => onAddHabit({ name: '30-minute workout', type: 'timed' })} />
                </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default HabitTracker;