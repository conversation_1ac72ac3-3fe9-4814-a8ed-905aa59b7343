import React from 'react';

const InfoCard: React.FC<{ pretitle: string; title: string; children: React.ReactNode; }> = ({ pretitle, title, children }) => (
    <div className="bg-[#F5F5F5] text-black p-8 lg:p-12 rounded-3xl flex flex-col justify-center min-h-[450px]">
        <p className="text-xs uppercase tracking-widest text-gray-500 mb-4">{pretitle}</p>
        <h3 className="text-2xl md:text-3xl font-bold mb-4 font-unbounded">{title}</h3>
        <p className="text-gray-700 leading-relaxed">{children}</p>
    </div>
);

const ImageCard: React.FC<{ src: string; alt: string; bgColor: string; }> = ({ src, alt, bgColor }) => (
    <div className={`p-8 rounded-3xl flex items-center justify-center ${bgColor} min-h-[450px]`}>
        <img src={src} alt={alt} className="w-full h-full object-cover rounded-xl shadow-lg" />
    </div>
);


const RevolutionSection: React.FC = () => {
    return (
        <section className="py-16 container mx-auto px-4">
            <div className="text-center mb-12">
                <p className="text-sm uppercase tracking-widest text-gray-400 mb-4">BOOST TEAM MORALE</p>
                <h2 className="text-5xl md:text-7xl font-bold font-unbounded">A revolution in <br/> team accountability</h2>
            </div>

            <div className="space-y-8">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <ImageCard src="https://images.pexels.com/photos/3184418/pexels-photo-3184418.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2" alt="Team members celebrating success" bgColor="bg-[#10B981]" />
                    <InfoCard pretitle="Stronger together" title="Shared goals build camaraderie and a powerful sense of collective momentum.">
                        When individuals see their contributions affecting the group's success on a live leaderboard, it fosters a positive and supportive team environment.
                    </InfoCard>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <InfoCard pretitle="Simple & Effective" title="Make habit tracking effortless and engaging for your entire organization.">
                        TeamStreak is designed to be intuitive. Log in with your work account, join a circle, and start tracking in seconds. No complex setup required.
                    </InfoCard>
                    <ImageCard src="https://images.pexels.com/photos/1092644/pexels-photo-1092644.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2" alt="TeamStreak mobile app interface" bgColor="bg-[#10B981]" />
                </div>
            </div>
        </section>
    );
};

export default RevolutionSection;