{"name": "@types/koa", "version": "2.15.0", "description": "TypeScript definitions for koa", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/koa", "license": "MIT", "contributors": [{"name": "j<PERSON><PERSON>", "githubUsername": "j<PERSON>lu", "url": "https://github.com/jkeylu"}, {"name": "<PERSON><PERSON>", "githubUsername": "brikou", "url": "https://github.com/brikou"}, {"name": "harry<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "harry<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/harryparkdotio"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "chatoo2412", "url": "https://github.com/chatoo2412"}, {"name": "<PERSON>", "githubUsername": "tellnes", "url": "https://github.com/tellnes"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/pku<PERSON>ynski"}, {"name": "vnoder", "githubUsername": "vnoder", "url": "https://github.com/vnoder"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/koa"}, "scripts": {}, "dependencies": {"@types/accepts": "*", "@types/content-disposition": "*", "@types/cookies": "*", "@types/http-assert": "*", "@types/http-errors": "*", "@types/keygrip": "*", "@types/koa-compose": "*", "@types/node": "*"}, "typesPublisherContentHash": "8dc62fabec3f299641f350581864ef97436d1887ea9201138ab1ba26a8c96983", "typeScriptVersion": "4.6"}