"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deserializeInvitationEvent = exports.deserializeInvitation = void 0;
const deserializeInvitation = (invitation) => ({
    object: invitation.object,
    id: invitation.id,
    email: invitation.email,
    state: invitation.state,
    acceptedAt: invitation.accepted_at,
    revokedAt: invitation.revoked_at,
    expiresAt: invitation.expires_at,
    organizationId: invitation.organization_id,
    inviterUserId: invitation.inviter_user_id,
    acceptedUserId: invitation.accepted_user_id,
    token: invitation.token,
    acceptInvitationUrl: invitation.accept_invitation_url,
    createdAt: invitation.created_at,
    updatedAt: invitation.updated_at,
});
exports.deserializeInvitation = deserializeInvitation;
const deserializeInvitationEvent = (invitation) => ({
    object: invitation.object,
    id: invitation.id,
    email: invitation.email,
    state: invitation.state,
    acceptedAt: invitation.accepted_at,
    revokedAt: invitation.revoked_at,
    expiresAt: invitation.expires_at,
    organizationId: invitation.organization_id,
    inviterUserId: invitation.inviter_user_id,
    acceptedUserId: invitation.accepted_user_id,
    createdAt: invitation.created_at,
    updatedAt: invitation.updated_at,
});
exports.deserializeInvitationEvent = deserializeInvitationEvent;
