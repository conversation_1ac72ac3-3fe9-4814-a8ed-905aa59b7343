
import React from 'react';
import { TeamStreakIcon, TwitterIcon, YoutubeIcon, LinkedInIcon } from './Icons';
import Button from './Button';
import { usePages, useNavigation } from '../App';

const FooterLink: React.FC<{ pageKey: string; children: React.ReactNode }> = ({ pageKey, children }) => {
    const { openPage } = usePages();
    return (
        <button onClick={() => openPage(pageKey)} className="text-gray-300 hover:text-white transition-colors text-lg text-left w-full">
            {children}
        </button>
    );
};

const Footer: React.FC = () => {
    const { navigateToPlayground } = useNavigation();
    
    return (
        <footer className="bg-[#141414] text-white mt-16 rounded-t-3xl">
            <div className="container mx-auto px-8 py-16">
                <div className="grid grid-cols-1 lg:grid-cols-12 gap-12">
                    {/* Left Section */}
                    <div className="lg:col-span-5">
                        <h3 className="text-3xl font-bold max-w-xs font-unbounded">TeamStreak: Build habits, together.</h3>
                        <div className="my-10">
                            <TeamStreakIcon className="text-[#10B981] w-24 h-24" />
                        </div>
                        <div className="flex gap-4">
                            <Button variant="yellow" onClick={navigateToPlayground}>Start tracking</Button>
                            <button className="px-6 py-3 rounded-full font-semibold border border-gray-600 hover:bg-gray-800 transition-colors">
                                Sign In
                            </button>
                        </div>
                    </div>

                    {/* Right Section */}
                    <div className="lg:col-span-7">
                        <div className="flex justify-between items-start border-b border-gray-700 pb-4 mb-8">
                            <div>
                                <p className="text-xs uppercase tracking-widest text-gray-400">Join the movement</p>
                            </div>
                            <div className="flex gap-6 text-xs text-gray-400">
                                <a href="#" className="hover:text-white">PRIVACY POLICY</a>
                                <a href="#" className="hover:text-white">TERMS OF SERVICE</a>
                            </div>
                        </div>

                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-8">
                            <div>
                                <h4 className="text-xs uppercase tracking-widest text-gray-400 mb-4">Product</h4>
                                <ul className="space-y-3">
                                    <li><FooterLink pageKey="Features">Features</FooterLink></li>
                                    <li><FooterLink pageKey="Pricing">Pricing</FooterLink></li>
                                    <li><FooterLink pageKey="Security">Security</FooterLink></li>
                                    <li><FooterLink pageKey="How It Works">How It Works</FooterLink></li>
                                </ul>
                            </div>
                             <div>
                                <h4 className="text-xs uppercase tracking-widest text-gray-400 mb-4">Company</h4>
                                <ul className="space-y-3">
                                    <li><FooterLink pageKey="About">About</FooterLink></li>
                                    <li><FooterLink pageKey="Contact">Contact</FooterLink></li>
                                </ul>
                            </div>
                        </div>
                        <div className="mt-12 flex justify-end">
                            <div className="flex gap-4">
                                <a href="#" className="text-gray-400 hover:text-white"><TwitterIcon /></a>
                                <a href="#" className="text-gray-400 hover:text-white"><YoutubeIcon /></a>
                                <a href="#" className="text-gray-400 hover:text-white"><LinkedInIcon /></a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div className="bg-[#047857] text-white text-center py-12 mt-16 rounded-t-3xl">
                <div className="container mx-auto px-6">
                    <h1 className="font-unbounded text-7xl md:text-9xl font-black tracking-tighter">TeamStreak</h1>
                    <p className="text-sm uppercase tracking-widest mt-4">BUILDING BETTER TEAMS, ONE HABIT AT A TIME</p>
                    <p className="text-sm font-bold uppercase tracking-widest mt-8 mb-8">Momentum is everything</p>
                    <p className="text-xs uppercase">COPYRIGHT ©2025 TEAMSTREAK.</p>
                </div>
            </div>
        </footer>
    );
};

export default Footer;