"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.serializeAuthenticateWithOrganizationSelectionOptions = void 0;
const serializeAuthenticateWithOrganizationSelectionOptions = (options) => ({
    grant_type: 'urn:workos:oauth:grant-type:organization-selection',
    client_id: options.clientId,
    client_secret: options.clientSecret,
    pending_authentication_token: options.pendingAuthenticationToken,
    organization_id: options.organizationId,
    ip_address: options.ipAddress,
    user_agent: options.userAgent,
});
exports.serializeAuthenticateWithOrganizationSelectionOptions = serializeAuthenticateWithOrganizationSelectionOptions;
