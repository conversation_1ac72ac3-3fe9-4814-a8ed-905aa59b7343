import React from 'react';
import Button from './Button';
import { useNavigation } from '../App';

const Kingdom: React.FC = () => {
    const { navigateToPlayground } = useNavigation();
    return (
        <section className="py-16 container mx-auto px-4">
            <div className="bg-[#F5F5F5] text-black rounded-3xl pt-16 lg:pt-24 overflow-hidden">
                <div className="text-center px-6">
                    <h2 className="text-5xl md:text-7xl font-bold max-w-4xl mx-auto font-unbounded">Ready to build unstoppable momentum?</h2>
                    <p className="mt-6 text-lg text-gray-700 max-w-2xl mx-auto">Start tracking habits with your team today and watch your collective productivity soar. It's free to get started.</p>
                    <div className="mt-10">
                        <Button variant="black" className="px-8 py-4 text-lg" onClick={navigateToPlayground}>Start tracking for free</Button>
                    </div>
                </div>
                <div className="mt-8">
                    <img 
                        src="https://images.pexels.com/photos/541216/pexels-photo-541216.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2" 
                        alt="Abstract graphic symbolizing growth and connection" 
                        className="w-full h-[300px] md:h-[500px] object-cover"
                    />
                </div>
            </div>
        </section>
    );
};

export default Kingdom;