export interface EmailVerification {
    object: 'email_verification';
    id: string;
    userId: string;
    email: string;
    expiresAt: string;
    code: string;
    createdAt: string;
    updatedAt: string;
}
export interface EmailVerificationEvent {
    object: 'email_verification';
    id: string;
    userId: string;
    email: string;
    expiresAt: string;
    createdAt: string;
    updatedAt: string;
}
export interface EmailVerificationResponse {
    object: 'email_verification';
    id: string;
    user_id: string;
    email: string;
    expires_at: string;
    code: string;
    created_at: string;
    updated_at: string;
}
export interface EmailVerificationEventResponse {
    object: 'email_verification';
    id: string;
    user_id: string;
    email: string;
    expires_at: string;
    created_at: string;
    updated_at: string;
}
