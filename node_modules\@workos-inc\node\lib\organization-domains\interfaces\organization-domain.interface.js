"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrganizationDomainVerificationStrategy = exports.OrganizationDomainState = void 0;
var OrganizationDomainState;
(function (OrganizationDomainState) {
    /**
     * @deprecated
     */
    OrganizationDomainState["LegacyVerified"] = "legacy_verified";
    OrganizationDomainState["Verified"] = "verified";
    OrganizationDomainState["Pending"] = "pending";
    OrganizationDomainState["Failed"] = "failed";
})(OrganizationDomainState || (exports.OrganizationDomainState = OrganizationDomainState = {}));
var OrganizationDomainVerificationStrategy;
(function (OrganizationDomainVerificationStrategy) {
    OrganizationDomainVerificationStrategy["Dns"] = "dns";
    OrganizationDomainVerificationStrategy["Manual"] = "manual";
})(OrganizationDomainVerificationStrategy || (exports.OrganizationDomainVerificationStrategy = OrganizationDomainVerificationStrategy = {}));
