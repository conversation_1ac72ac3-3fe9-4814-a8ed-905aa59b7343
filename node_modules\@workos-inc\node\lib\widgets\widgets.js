"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Widgets = void 0;
const get_token_1 = require("./interfaces/get-token");
class Widgets {
    constructor(workos) {
        this.workos = workos;
    }
    getToken(payload) {
        return __awaiter(this, void 0, void 0, function* () {
            const { data } = yield this.workos.post('/widgets/token', (0, get_token_1.serializeGetTokenOptions)(payload));
            return (0, get_token_1.deserializeGetTokenResponse)(data).token;
        });
    }
}
exports.Widgets = Widgets;
