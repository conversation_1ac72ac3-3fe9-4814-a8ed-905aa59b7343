import React from 'react';
import { UsersIcon, CheckCircleIcon, TrendingUpIcon, FlameIcon } from './Icons';

interface AssetCardProps {
    bgColor: string;
    icon: React.ReactNode;
    category: string;
    title: string;
}

const AssetCard: React.FC<AssetCardProps> = ({ bgColor, icon, category, title }) => (
    <div className={`rounded-3xl p-8 flex flex-col aspect-square ${bgColor}`}>
        <div className="flex-grow flex items-center justify-center">
            <div className="bg-black text-white w-2/3 h-2/3 flex items-center justify-center rounded-3xl">
                {icon}
            </div>
        </div>
        <div className="text-black mt-4">
            <p className="text-xs uppercase tracking-widest">{category}</p>
            <h4 className="font-bold text-xl font-unbounded">{title}</h4>
        </div>
    </div>
);

const AssetGrid: React.FC = () => {
    return (
        <section className="bg-[#F5F5F5] text-black py-24 my-16 mx-4 rounded-3xl">
            <div className="container mx-auto px-6">
                <div className="text-center mb-12">
                    <h2 className="text-5xl md:text-7xl font-bold font-unbounded">Everything you need <br/> to succeed</h2>
                </div>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
                    <AssetCard 
                        bgColor="bg-[#A9B9F8]" 
                        icon={<UsersIcon className="w-16 h-16 text-[#A9B9F8]" />} 
                        category="WORKOS LOGIN" 
                        title="Create private circles" 
                    />
                    <AssetCard 
                        bgColor="bg-[#F8AE75]" 
                        icon={<CheckCircleIcon className="w-16 h-16 text-[#F8AE75]" />} 
                        category="DAILY CHECK-INS" 
                        title="Track any habit" 
                    />
                    <AssetCard 
                        bgColor="bg-[#F8B4E3]" 
                        icon={<TrendingUpIcon className="w-16 h-16 text-[#F8B4E3]" />} 
                        category="REAL-TIME" 
                        title="View leaderboards" 
                    />
                     <AssetCard 
                        bgColor="bg-[#E0E0E0]" 
                        icon={<FlameIcon className="w-16 h-16 text-black" />} 
                        category="MOTIVATION" 
                        title="Build team streaks" 
                    />
                </div>
                 <div className="text-center mt-12">
                    <button className="bg-black text-white px-8 py-4 rounded-full font-semibold transition-transform duration-200 ease-in-out hover:scale-105">
                        Create your first circle
                    </button>
                </div>
            </div>
        </section>
    );
};

export default AssetGrid;
