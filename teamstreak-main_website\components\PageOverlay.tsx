import React, { useEffect } from 'react';
import { CloseIcon } from './Icons';
import * as Pages from './Pages';

const pageMap: { [key: string]: React.FC } = {
  features: Pages.FeaturesPage,
  pricing: Pages.PricingPage,
  howitworks: Pages.HowItWorksPage,
  security: Pages.SecurityPage,
  about: Pages.AboutPage,
  contact: Pages.ContactPage,
};


const PageOverlay: React.FC<{ page: string; onClose: () => void }> = ({ page, onClose }) => {
    const PageComponent = pageMap[page.toLowerCase().replace(/\s/g, '')];

    useEffect(() => {
        const handleKeyDown = (event: KeyboardEvent) => {
            if (event.key === 'Escape') {
                onClose();
            }
        };
        document.body.style.overflow = 'hidden';
        window.addEventListener('keydown', handleKeyDown);
        return () => {
            document.body.style.overflow = 'unset';
            window.removeEventListener('keydown', handleKeyDown);
        };
    }, [onClose]);

    return (
        <div 
            className="fixed inset-0 bg-[#101010]/90 backdrop-blur-md z-50 animate-fade-in"
            onClick={onClose}
            role="dialog"
            aria-modal="true"
            aria-label={`${page} page`}
        >
            <div 
                className="w-full h-full overflow-y-auto"
                onClick={e => e.stopPropagation()}
            >
                <div className="container mx-auto px-6 py-24 relative min-h-full flex flex-col justify-center">
                    <button 
                        onClick={onClose} 
                        className="absolute top-8 right-6 text-white hover:text-[#10B981] transition-colors z-10"
                        aria-label="Close page"
                    >
                        <CloseIcon className="w-10 h-10" />
                    </button>
                    {PageComponent ? <PageComponent /> : <div><h1 className="text-5xl font-unbounded font-bold text-[#10B981]">Page Not Found</h1><p className="mt-4 text-lg">The page '{page}' could not be found.</p></div>}
                </div>
            </div>
        </div>
    );
};

export default PageOverlay;