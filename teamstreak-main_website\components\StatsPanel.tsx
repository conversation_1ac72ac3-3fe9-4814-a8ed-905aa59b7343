import React, { useMemo } from 'react';
import type { User } from '../types';
import { FlameIcon, CheckCircleIcon, ProgressChartIcon } from './icons';

const StatRow: React.FC<{ title: string; value: string | number; icon: React.ReactNode }> = ({ title, value, icon }) => (
    <div className="flex items-center gap-4 p-4 bg-gray-900/50 rounded-xl">
        <div className="flex-shrink-0 w-8 h-8 flex items-center justify-center bg-gray-800 rounded-full text-emerald-500">
            {icon}
        </div>
        <div className="flex-1">
            <p className="text-sm text-gray-400 uppercase tracking-wider">{title}</p>
            <p className="font-stats text-3xl text-white tracking-wider">{value}</p>
        </div>
    </div>
);

const SparklineChart: React.FC<{ data: number[] }> = ({ data }) => {
    const width = 100;
    const height = 40;
    if (data.length < 2) return null;

    const maxVal = Math.max(...data, 1);
    const points = data.map((d, i) => {
        const x = (i / (data.length - 1)) * width;
        const y = height - (d / maxVal) * (height - 5) - 2; 
        return `${x},${y}`;
    }).join(' ');

    const areaPoints = `${points} ${width},${height} 0,${height}`;

    return (
        <svg viewBox={`0 0 ${width} ${height}`} className="w-full h-auto">
            <defs>
                <linearGradient id="sparkline-gradient" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="0%" stopColor="#10b981" stopOpacity="0.3" />
                    <stop offset="100%" stopColor="#10b981" stopOpacity="0" />
                </linearGradient>
            </defs>
            <polygon points={areaPoints} fill="url(#sparkline-gradient)" />
            <polyline
                fill="none"
                stroke="#10b981"
                strokeWidth="2"
                points={points}
                strokeLinecap="round"
                strokeLinejoin="round"
            />
        </svg>
    );
};

const StatsPanel: React.FC<{ user: User }> = ({ user }) => {
    const stats = useMemo(() => {
        const totalHabits = user.habits.length;
        if (totalHabits === 0) {
            return { currentStreak: 0, completion: "0%", habitsDone: "0/0", weeklyData: [0,0,0,0,0,0,0] };
        }
        
        const allStreaks = user.habits.map(h => h.streak);
        const currentStreak = Math.max(...allStreaks, 0);

        const habitsDoneToday = user.habits.filter(h => h.isCompletedToday).length;
        const completionPercentage = Math.round((habitsDoneToday / totalHabits) * 100);
        
        const weeklyData = [0.2, 0.5, 0.4, 0.8, 0.6, 0.9, completionPercentage / 100];

        return {
            currentStreak,
            completion: `${completionPercentage}%`,
            habitsDone: `${habitsDoneToday}/${totalHabits}`,
            weeklyData,
        };
    }, [user.habits]);
    
    return (
        <div className="bg-white/5 border border-gray-800/70 p-6 rounded-3xl shadow-lg">
            <h3 className="font-bold text-xl mb-4">Your Stats</h3>
            <div className="space-y-4">
                <StatRow title="Best Streak" value={stats.currentStreak} icon={<FlameIcon className="h-5 w-5" />} />
                <StatRow title="Habits Done" value={stats.habitsDone} icon={<CheckCircleIcon className="h-5 w-5" />} />
                <StatRow title="Today's Progress" value={stats.completion} icon={<ProgressChartIcon className="h-5 w-5" />} />
                
                <div className="bg-gray-900/50 p-4 rounded-xl">
                    <p className="text-sm text-gray-400 uppercase tracking-wider mb-2">Weekly Completion</p>
                    <SparklineChart data={stats.weeklyData} />
                </div>
            </div>
        </div>
    );
};

export default StatsPanel;