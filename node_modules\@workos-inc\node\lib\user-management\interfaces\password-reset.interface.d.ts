export interface PasswordReset {
    object: 'password_reset';
    id: string;
    userId: string;
    email: string;
    passwordResetToken: string;
    passwordResetUrl: string;
    expiresAt: string;
    createdAt: string;
}
export interface PasswordResetEvent {
    object: 'password_reset';
    id: string;
    userId: string;
    email: string;
    expiresAt: string;
    createdAt: string;
}
export interface PasswordResetResponse {
    object: 'password_reset';
    id: string;
    user_id: string;
    email: string;
    password_reset_token: string;
    password_reset_url: string;
    expires_at: string;
    created_at: string;
}
export interface PasswordResetEventResponse {
    object: 'password_reset';
    id: string;
    user_id: string;
    email: string;
    expires_at: string;
    created_at: string;
}
