export type AuthenticationRadarRiskDetectedEventData = {
    authMethod: string;
    action: 'signup' | 'login';
    control: string | null;
    blocklistType: string | null;
    ipAddress: string | null;
    userAgent: string | null;
    userId: string;
    email: string;
};
export interface AuthenticationRadarRiskDetectedEventResponseData {
    auth_method: string;
    action: 'signup' | 'login';
    control: string | null;
    blocklist_type: string | null;
    ip_address: string | null;
    user_agent: string | null;
    user_id: string;
    email: string;
}
