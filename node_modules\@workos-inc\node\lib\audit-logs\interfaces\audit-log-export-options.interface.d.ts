export interface AuditLogExportOptions {
    actions?: string[];
    /**
     * @deprecated Please use `actorNames` instead.
     */
    actors?: string[];
    actorNames?: string[];
    actorIds?: string[];
    organizationId: string;
    rangeEnd: Date;
    rangeStart: Date;
    targets?: string[];
}
export interface SerializedAuditLogExportOptions {
    actions?: string[];
    actors?: string[];
    actor_names?: string[];
    actor_ids?: string[];
    organization_id: string;
    range_end: string;
    range_start: string;
    targets?: string[];
}
