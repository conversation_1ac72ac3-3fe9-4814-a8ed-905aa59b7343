export declare enum ConnectionType {
    ADFSSAML = "ADFSSAML",
    AdpOidc = "AdpOidc",
    AppleOAuth = "AppleOAuth",
    Auth0SAML = "Auth0SAML",
    AzureSAML = "AzureSAML",
    CasSAML = "CasSAML",
    ClassLinkSAML = "ClassLinkSAML",
    CloudflareSAML = "CloudflareSAML",
    CyberArkSAML = "CyberArkSAML",
    DuoSAML = "DuoSAML",
    GenericOIDC = "GenericOIDC",
    GenericSAML = "GenericSAML",
    GitHubOAuth = "GitHubOAuth",
    GoogleOAuth = "GoogleOAuth",
    GoogleSAML = "GoogleSAML",
    JumpCloudSAML = "JumpCloudSAML",
    KeycloakSAML = "KeycloakSAML",
    LastPassSAML = "LastPassSAML",
    LoginGovOidc = "LoginGovOidc",
    MagicLink = "MagicLink",
    MicrosoftOAuth = "MicrosoftOAuth",
    MiniOrangeSAML = "MiniOrangeSAML",
    NetIqSAML = "NetIqSAML",
    OktaSAML = "OktaSAML",
    OneLoginSAML = "OneLoginSAML",
    OracleSAML = "OracleSAML",
    PingFederateSAML = "PingFederateSAML",
    PingOneSAML = "PingOneSAML",
    RipplingSAML = "RipplingSAML",
    SalesforceSAML = "SalesforceSAML",
    ShibbolethGenericSAML = "ShibbolethGenericSAML",
    ShibbolethSAML = "ShibbolethSAML",
    SimpleSamlPhpSAML = "SimpleSamlPhpSAML",
    VMwareSAML = "VMwareSAML"
}
