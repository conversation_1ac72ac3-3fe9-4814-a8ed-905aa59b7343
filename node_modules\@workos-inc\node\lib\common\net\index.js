"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createHttpClient = void 0;
const fetch_client_1 = require("./fetch-client");
const node_client_1 = require("./node-client");
function createHttpClient(baseURL, options, fetchFn) {
    if (typeof fetch !== 'undefined' || typeof fetchFn !== 'undefined') {
        return new fetch_client_1.FetchHttpClient(baseURL, options, fetchFn);
    }
    else {
        return new node_client_1.NodeHttpClient(baseURL, options);
    }
}
exports.createHttpClient = createHttpClient;
__exportStar(require("./fetch-client"), exports);
__exportStar(require("./node-client"), exports);
__exportStar(require("./http-client"), exports);
