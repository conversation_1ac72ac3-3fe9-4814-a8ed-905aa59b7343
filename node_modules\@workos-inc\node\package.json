{"version": "7.69.1", "name": "@workos-inc/node", "author": "WorkOS", "description": "A Node wrapper for the WorkOS API", "homepage": "https://github.com/workos-inc/workos-node", "license": "MIT", "keywords": ["workos"], "engines": {"node": ">=16"}, "typings": "lib/index.d.ts", "files": ["lib/"], "repository": {"type": "git", "url": "git+https://github.com/workos-inc/workos-node.git"}, "bugs": {"url": "https://github.com/workos-inc/workos-node/issues"}, "scripts": {"clean": "rm -rf lib", "build": "tsc -p .", "lint": "tslint -p tsconfig.json -c tslint.json", "test": "jest", "test:watch": "jest --watch", "test:worker": "jest src/worker.spec.ts", "prettier": "prettier \"src/**/*.{js,ts,tsx}\" --check", "format": "prettier \"src/**/*.{js,ts,tsx}\" --write", "prepublishOnly": "npm run build"}, "dependencies": {"iron-session": "~6.3.1", "jose": "~5.6.3", "leb": "^1.0.0", "pluralize": "8.0.0", "qs": "6.14.0"}, "devDependencies": {"@peculiar/webcrypto": "^1.4.5", "@types/jest": "29.5.3", "@types/node": "14.18.54", "@types/pluralize": "0.0.30", "jest": "29.6.2", "jest-environment-miniflare": "^2.14.2", "jest-fetch-mock": "^3.0.3", "nock": "^13.5.5", "prettier": "2.8.8", "supertest": "6.3.3", "ts-jest": "29.1.3", "tslint": "6.1.3", "typescript": "5.1.6"}, "exports": {"types": "./lib/index.d.ts", "workerd": {"import": "./lib/index.worker.js", "default": "./lib/index.worker.js"}, "edge-light": {"import": "./lib/index.worker.js", "default": "./lib/index.worker.js"}, "default": {"import": "./lib/index.js", "default": "./lib/index.js"}}}