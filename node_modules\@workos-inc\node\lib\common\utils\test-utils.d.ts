import { MockParams } from 'jest-fetch-mock';
export declare function fetchOnce(response?: {}, { status, headers, ...rest }?: MockParams): import("jest-fetch-mock").FetchMock;
export declare function fetchURL(): string | Request | undefined;
export declare function fetchSearchParams(): {
    [k: string]: string;
};
export declare function fetchHeaders(): HeadersInit | undefined;
export declare function fetchMethod(): string | undefined;
export declare function fetchBody({ raw }?: {
    raw?: boolean | undefined;
}): any;
