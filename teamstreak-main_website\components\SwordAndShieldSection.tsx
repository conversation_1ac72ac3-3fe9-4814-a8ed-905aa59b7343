import React from 'react';
import Button from './Button';
import { TrophyIcon, HeartIcon } from './Icons';

const InfoCard: React.FC<{ title: string; children: React.ReactNode; buttons: React.ReactNode; }> = ({ title, children, buttons }) => (
    <div className="bg-[#F5F5F5] text-black p-8 lg:p-12 rounded-3xl flex flex-col justify-center min-h-[450px]">
        <h3 className="text-3xl md:text-5xl font-bold mb-4 font-unbounded">{title}</h3>
        <p className="text-gray-700 leading-relaxed mb-8">{children}</p>
        <div className="flex items-center gap-4">
            {buttons}
        </div>
    </div>
);

const ImageCard: React.FC<{ src: string; alt: string; bgColor: string; icon: React.ReactNode }> = ({ src, alt, bgColor, icon }) => (
    <div className={`relative p-8 rounded-3xl flex items-center justify-center ${bgColor} min-h-[450px] overflow-hidden`}>
        <div className="absolute top-8 left-8 text-white z-10">{icon}</div>
        <img src={src} alt={alt} className="absolute top-0 left-0 w-full h-full object-cover" />
    </div>
);

const SwordAndShieldSection: React.FC = () => {
    return (
        <section className="py-16 container mx-auto px-4">
            <div className="text-center mb-12">
                <p className="text-sm uppercase tracking-widest text-gray-400 mb-4">COMPETE & SUPPORT</p>
                <h2 className="text-5xl md:text-7xl font-bold font-unbounded">Friendly competition,<br/> unwavering support</h2>
            </div>

            <div className="space-y-8">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <ImageCard 
                        src="https://images.pexels.com/photos/5083407/pexels-photo-5083407.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2" 
                        alt="TeamStreak leaderboard" 
                        bgColor="bg-[#31118A]" 
                        icon={<TrophyIcon className="w-8 h-8 text-white"/>} 
                    />
                    <InfoCard 
                        title="Climb the Leaderboard" 
                        buttons={<>
                            <Button variant="black">View Live Demo</Button>
                        </>}>
                        Track daily progress and see how you stack up against your teammates. A little friendly competition is a powerful way to ignite your team's motivation and drive consistent action.
                    </InfoCard>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                     <InfoCard 
                        title="Never Break the Chain"
                        buttons={<Button variant="black">Learn about streaks</Button>}>
                        Support your teammates and hold each other accountable. Daily check-ins and shared streaks build a powerful sense of collective momentum and ensure no one gets left behind.
                    </InfoCard>
                    <ImageCard 
                        src="https://images.pexels.com/photos/4348401/pexels-photo-4348401.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2" 
                        alt="TeamStreak daily check-in UI" 
                        bgColor="bg-[#A30041]"
                        icon={<HeartIcon className="w-8 h-8 text-white" />} 
                    />
                </div>
            </div>
        </section>
    );
};

export default SwordAndShieldSection;