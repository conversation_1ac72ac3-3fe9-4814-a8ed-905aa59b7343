export interface Invitation {
    object: 'invitation';
    id: string;
    email: string;
    state: 'pending' | 'accepted' | 'expired' | 'revoked';
    acceptedAt: string | null;
    revokedAt: string | null;
    expiresAt: string;
    organizationId: string | null;
    inviterUserId: string | null;
    acceptedUserId: string | null;
    token: string;
    acceptInvitationUrl: string;
    createdAt: string;
    updatedAt: string;
}
export interface InvitationEvent {
    object: 'invitation';
    id: string;
    email: string;
    state: 'pending' | 'accepted' | 'expired' | 'revoked';
    acceptedAt: string | null;
    revokedAt: string | null;
    expiresAt: string;
    organizationId: string | null;
    inviterUserId: string | null;
    acceptedUserId: string | null;
    createdAt: string;
    updatedAt: string;
}
export interface InvitationResponse {
    object: 'invitation';
    id: string;
    email: string;
    state: 'pending' | 'accepted' | 'expired' | 'revoked';
    accepted_at: string | null;
    revoked_at: string | null;
    expires_at: string;
    organization_id: string | null;
    inviter_user_id: string | null;
    accepted_user_id: string | null;
    token: string;
    accept_invitation_url: string;
    created_at: string;
    updated_at: string;
}
export interface InvitationEventResponse {
    object: 'invitation';
    id: string;
    email: string;
    state: 'pending' | 'accepted' | 'expired' | 'revoked';
    accepted_at: string | null;
    revoked_at: string | null;
    expires_at: string;
    organization_id: string | null;
    inviter_user_id: string | null;
    accepted_user_id: string | null;
    created_at: string;
    updated_at: string;
}
