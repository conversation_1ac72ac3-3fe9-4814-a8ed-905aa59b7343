import React, { useState, useCallback, useEffect } from 'react';
import Header from './components/PlaygroundHeader';
import HabitTracker from './components/HabitTracker';
import GroupProgress from './components/GroupProgress';
import ActivityFeed from './components/ActivityFeed';
import StatsPanel from './components/StatsPanel';
import CalendarView from './components/CalendarView';
import QuickAddModal from './components/QuickAddModal';
import { useMockData } from './hooks/useMockData';
import type { User, ActivityLog, Habit } from './types';

const PlaygroundApp: React.FC = () => {
  const { users, setUsers, activityLog, setActivityLog, currentUser, setCurrentUser } = useMockData();
  const [celebration, setCelebration] = useState<{ habitId: string; streak: number } | null>(null);
  const [isQuickAddModalOpen, setQuickAddModalOpen] = useState(false);

  const handleAddHabit = useCallback((habitDetails: Partial<Habit>) => {
    if (!habitDetails.name?.trim()) return;

    const newHabit: Habit = {
      id: `h${Date.now()}`,
      name: habitDetails.name,
      type: habitDetails.type || 'check-off',
      streak: 0,
      longestStreak: 0,
      isCompletedToday: false,
      history: {},
      timeWindow: habitDetails.timeWindow,
      ...habitDetails,
    };

    const updatedUsers = users.map(user => {
      if (user.id === currentUser.id) {
        return { ...user, habits: [...user.habits, newHabit] };
      }
      return user;
    });

    setUsers(updatedUsers);
    const updatedCurrentUser = updatedUsers.find(u => u.id === currentUser.id);
    if(updatedCurrentUser) setCurrentUser(updatedCurrentUser);
    setQuickAddModalOpen(false);
  }, [users, currentUser, setUsers, setCurrentUser]);

  const handleToggleHabit = useCallback((habitId: string, isCompleted: boolean) => {
    const now = new Date();
    let habitToCelebrate: { habitId: string; streak: number } | null = null;

    // Update user's habits
    const updatedUsers = users.map(user => {
      if (user.id === currentUser.id) {
        const updatedHabits = user.habits.map(habit => {
          if (habit.id === habitId) {
            const newStreak = isCompleted ? habit.streak + 1 : 0;
            if (isCompleted && [7, 14, 21, 30, 50, 75, 100].includes(newStreak)) {
                habitToCelebrate = { habitId, streak: newStreak };
            }
            const newHistory = { ...habit.history, [now.toISOString().split('T')[0]]: isCompleted };
            const longestStreak = Math.max(habit.longestStreak || 0, newStreak);
            return { ...habit, isCompletedToday: isCompleted, streak: newStreak, history: newHistory, lastCompletedTimestamp: isCompleted ? now : undefined };
          }
          return habit;
        });
        return { ...user, habits: updatedHabits };
      }
      return user;
    });

    setUsers(updatedUsers);
    const updatedCurrentUser = updatedUsers.find(u => u.id === currentUser.id);
    if(updatedCurrentUser) setCurrentUser(updatedCurrentUser);

    if (habitToCelebrate) {
        setCelebration(habitToCelebrate);
        setTimeout(() => setCelebration(null), 4000); // Confetti lasts 4 seconds
    }


    // Add to activity log
    const habitName = currentUser.habits.find(h => h.id === habitId)?.name || 'a habit';
    if(isCompleted) {
        const newLog: ActivityLog = {
            id: Date.now().toString(),
            userId: currentUser.id,
            userName: currentUser.name,
            userAvatar: currentUser.avatar,
            action: `completed '${habitName}'`,
            timestamp: now,
        };
        setActivityLog(prevLog => [newLog, ...prevLog]);
    }
  }, [users, currentUser, setUsers, setActivityLog, setCurrentUser]);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Example shortcuts from the spec
      if (e.key.toLowerCase() === 'j') {
        console.log('Navigate down');
      }
      if (e.key.toLowerCase() === 'k') {
        console.log('Navigate up');
      }
      if (e.key.toLowerCase() === 'f') {
        console.log('Focus search');
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, []);


  return (
    <div className="min-h-screen bg-black text-white selection:bg-emerald-500 selection:text-black">
      <Header user={currentUser} />
      <main className="p-4 sm:p-8 md:p-10 lg:p-14">
        <div className="max-w-8xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-5 gap-10">
            {/* Left Column (Main) */}
            <div className="lg:col-span-3 space-y-10">
              <HabitTracker user={currentUser} onToggleHabit={handleToggleHabit} celebration={celebration} onQuickAdd={() => setQuickAddModalOpen(true)} onAddHabit={handleAddHabit} />
              <GroupProgress users={users} currentUserId={currentUser.id} />
            </div>

            {/* Right Column (Sidebar) */}
            <div className="lg:col-span-2 space-y-10">
              <StatsPanel user={currentUser} />
              <CalendarView user={currentUser} />
              <ActivityFeed logs={activityLog} />
            </div>
          </div>
        </div>
      </main>
      <footer className="px-4 sm:px-8 md:px-10 lg:px-14 mt-20">
          <div className="bg-emerald-500 text-black p-12 md:p-20 text-center rounded-3xl max-w-8xl mx-auto">
              <h2 className="font-brand text-6xl md:text-8xl">TeamStreak</h2>
              <p className="mt-4 font-mono text-sm tracking-widest uppercase">The Stronghold of the Committed</p>
              <p className="mt-8 font-bold">HOLD STRONG</p>
          </div>
          <div className="text-center p-6 max-w-8xl mx-auto">
              <p className="text-gray-600 text-xs">COPYRIGHT ©2025 TEAMSTREAK.</p>
          </div>
      </footer>
      <QuickAddModal
        isOpen={isQuickAddModalOpen}
        onClose={() => setQuickAddModalOpen(false)}
        onAddHabit={handleAddHabit}
      />
    </div>
  );
};

export default PlaygroundApp;
