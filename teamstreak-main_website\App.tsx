import React, { useState, createContext, useContext, useCallback, useEffect } from 'react';
import Header from './components/Header';
import Hero from './components/Hero';
import Stats from './components/Stats';
import RevolutionSection from './components/RevolutionSection';
import SwordAndShieldSection from './components/SwordAndShieldSection';
import AssetGrid from './components/AssetGrid';
import Support from './components/Support';
import Testimonials from './components/Testimonials';
import Kingdom from './components/Kingdom';
import Footer from './components/Footer';
import PageOverlay from './components/PageOverlay';

interface PageContextType {
  openPage: (page: string | null) => void;
  activePage: string | null;
}

export const PageContext = createContext<PageContextType | null>(null);

export const usePages = () => {
  const context = useContext(PageContext);
  if (!context) {
    throw new Error('usePages must be used within a PageProvider');
  }
  return context;
};

const PageProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [activePage, setActivePage] = useState<string | null>(null);

  const openPage = useCallback((page: string | null) => {
    setActivePage(page);
  }, []);

  return (
    <PageContext.Provider value={{ openPage, activePage }}>
      {children}
      {activePage && <PageOverlay page={activePage} onClose={() => openPage(null)} />}
    </PageContext.Provider>
  );
};


import PlaygroundApp from './PlaygroundApp';

interface NavigationContextType {
  navigateToPlayground: () => void;
}

export const NavigationContext = createContext<NavigationContextType | null>(null);

export const useNavigation = () => {
  const context = useContext(NavigationContext);
  if (!context) {
    throw new Error('useNavigation must be used within a NavigationProvider');
  }
  return context;
};

const App: React.FC = () => {
  const [showPlayground, setShowPlayground] = useState(false);
  const [isFadingOut, setIsFadingOut] = useState(false);

  const navigateToPlayground = () => {
    setIsFadingOut(true);
    setTimeout(() => {
      setShowPlayground(true);
    }, 500); // 500ms for fade-out animation
  };

  return (
    <PageProvider>
      <NavigationContext.Provider value={{ navigateToPlayground }}>
        <div className={`bg-[#101010] text-white overflow-x-hidden ${isFadingOut && !showPlayground ? 'fade-out' : ''}`}>
          <style>
            {`
              .fade-out {
                animation: fadeOut 0.5s ease-out forwards;
              }

              @keyframes fadeOut {
                from {
                  opacity: 1;
                }
                to {
                  opacity: 0;
                }
              }
            `}
          </style>
          {showPlayground ? (
            <PlaygroundApp />
          ) : (
            <>
              <Header />
              <main>
                <Hero />
                <Stats />
                <RevolutionSection />
                <SwordAndShieldSection />
                <AssetGrid />
                <Support />
                <Testimonials />
                <Kingdom />
              </main>
              <Footer />
            </>
          )}
        </div>
      </NavigationContext.Provider>
    </PageProvider>
  );
};

export default App;