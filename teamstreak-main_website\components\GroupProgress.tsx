import React from 'react';
import type { User } from '../types';
import { FlameIcon } from './icons';

interface GroupProgressProps {
    users: User[];
    currentUserId: string;
}

const GroupProgress: React.FC<GroupProgressProps> = ({ users, currentUserId }) => {
    const sortedUsers = [...users].sort((a, b) => {
        const progressA = (a.habits.filter(h => h.isCompletedToday).length / a.habits.length) || 0;
        const progressB = (b.habits.filter(h => h.isCompletedToday).length / b.habits.length) || 0;
        if (progressB !== progressA) return progressB - progressA;
        const streakA = Math.max(0, ...a.habits.map(h => h.streak));
        const streakB = Math.max(0, ...b.habits.map(h => h.streak));
        return streakB - streakA;
    });

    return (
        <div className="bg-white/5 border border-gray-800/70 p-6 rounded-3xl shadow-lg">
            <h2 className="font-brand text-5xl text-white mb-6">Leaderboard</h2>
            <div className="space-y-4">
                {sortedUsers.map((user, index) => {
                    const completedCount = user.habits.filter(h => h.isCompletedToday).length;
                    const totalCount = user.habits.length;
                    const progress = totalCount > 0 ? (completedCount / totalCount) * 100 : 0;
                    const bestStreak = Math.max(0, ...user.habits.map(h => h.streak));
                    const rank = index + 1;

                    return (
                        <div key={user.id} className={`p-4 rounded-lg flex items-center gap-4 transition-all ${user.id === currentUserId ? 'bg-emerald-500/15 border-2 border-emerald-500/40' : 'bg-gray-900/50'}`}>
                            <div className="flex items-center justify-center w-8 font-stats text-3xl text-gray-500">{rank}</div>
                            <img src={user.avatar} alt={user.name} className="w-12 h-12 rounded-full border-2 border-gray-700" />
                            <div className="flex-grow">
                                <div className="flex justify-between items-center mb-1.5">
                                    <p className="font-semibold text-white">
                                        {user.name} {user.id === currentUserId && <span className="text-xs text-emerald-500/80">(You)</span>}
                                    </p>
                                    <div className="flex items-center gap-4">
                                        <p className="text-sm font-mono text-gray-400">{completedCount}/{totalCount}</p>
                                        <div className="flex items-center gap-1.5 text-emerald-500" title={`Best Streak: ${bestStreak} days`}>
                                            <FlameIcon className="w-4 h-4" />
                                            <span className="font-stats text-xl tracking-wider">{bestStreak}</span>
                                        </div>
                                    </div>
                                </div>
                                <div className="w-full bg-gray-700/50 rounded-full h-2">
                                    <div className="bg-gradient-to-r from-emerald-600 to-emerald-500 h-2 rounded-full transition-all duration-500" style={{ width: `${progress}%` }}></div>
                                </div>
                            </div>
                        </div>
                    );
                })}
            </div>
        </div>
    );
};

export default GroupProgress;